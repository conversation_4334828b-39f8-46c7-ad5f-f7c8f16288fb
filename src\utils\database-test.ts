// src/utils/database-test.ts
/**
 * Database connectivity and table structure verification utility
 * This file helps verify that all required tables exist and have the correct structure
 */

import { supabase } from '../lib/supabase';
import { DATABASE_TABLES, DATABASE_COLUMNS } from '../constants/database';

interface TableInfo {
  table_name: string;
  column_name: string;
  data_type: string;
  is_nullable: string;
}

/**
 * Test database connectivity
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    console.log('🔍 Testing database connection...');
    
    // Simple query to test connection
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1);

    if (error) {
      console.error('❌ Database connection failed:', error);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection error:', error);
    return false;
  }
}

/**
 * Verify that all required tables exist
 */
export async function verifyTablesExist(): Promise<{ exists: string[]; missing: string[] }> {
  try {
    console.log('🔍 Verifying table existence...');
    
    const requiredTables = [
      DATABASE_TABLES.CLASSES,
      DATABASE_TABLES.SECTIONS,
      DATABASE_TABLES.ACADEMIC_YEARS,
      DATABASE_TABLES.GUARDIAN_RELATIONS,
      DATABASE_TABLES.STUDENTS
    ];

    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', requiredTables);

    if (error) {
      console.error('❌ Error checking tables:', error);
      return { exists: [], missing: requiredTables };
    }

    const existingTables = data?.map(row => row.table_name) || [];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));

    console.log('📊 Table verification results:', {
      exists: existingTables,
      missing: missingTables
    });

    return { exists: existingTables, missing: missingTables };
  } catch (error) {
    console.error('❌ Error verifying tables:', error);
    return { exists: [], missing: [] };
  }
}

/**
 * Get table structure for a specific table
 */
export async function getTableStructure(tableName: string): Promise<TableInfo[]> {
  try {
    console.log(`🔍 Getting structure for table: ${tableName}`);
    
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('table_name, column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .order('ordinal_position');

    if (error) {
      console.error(`❌ Error getting structure for ${tableName}:`, error);
      return [];
    }

    console.log(`✅ Structure for ${tableName}:`, data);
    return data || [];
  } catch (error) {
    console.error(`❌ Error getting table structure for ${tableName}:`, error);
    return [];
  }
}

/**
 * Test data retrieval from master data tables
 */
export async function testMasterDataRetrieval(): Promise<{
  classes: number;
  sections: number;
  academicYears: number;
  guardianRelations: number;
}> {
  try {
    console.log('🔍 Testing master data retrieval...');
    
    const results = await Promise.allSettled([
      supabase.from(DATABASE_TABLES.CLASSES).select('id').eq('is_active', true),
      supabase.from(DATABASE_TABLES.SECTIONS).select('id').eq('is_active', true),
      supabase.from(DATABASE_TABLES.ACADEMIC_YEARS).select('id').eq('is_active', true),
      supabase.from(DATABASE_TABLES.GUARDIAN_RELATIONS).select('id').eq('is_active', true)
    ]);

    const counts = {
      classes: 0,
      sections: 0,
      academicYears: 0,
      guardianRelations: 0
    };

    const tableNames = ['classes', 'sections', 'academicYears', 'guardianRelations'];
    
    results.forEach((result, index) => {
      const tableName = tableNames[index] as keyof typeof counts;
      if (result.status === 'fulfilled' && !result.value.error) {
        counts[tableName] = result.value.data?.length || 0;
        console.log(`✅ ${tableName}: ${counts[tableName]} records`);
      } else {
        console.error(`❌ ${tableName} query failed:`, 
          result.status === 'rejected' ? result.reason : result.value.error);
      }
    });

    return counts;
  } catch (error) {
    console.error('❌ Error testing master data retrieval:', error);
    return { classes: 0, sections: 0, academicYears: 0, guardianRelations: 0 };
  }
}

/**
 * Run comprehensive database verification
 */
export async function runDatabaseVerification(): Promise<void> {
  console.log('🚀 Starting comprehensive database verification...');
  
  // Test connection
  const connectionOk = await testDatabaseConnection();
  if (!connectionOk) {
    console.error('❌ Database verification failed: No connection');
    return;
  }

  // Verify tables exist
  const { exists, missing } = await verifyTablesExist();
  if (missing.length > 0) {
    console.error('❌ Missing required tables:', missing);
  }

  // Test data retrieval
  const counts = await testMasterDataRetrieval();
  
  console.log('📊 Database verification summary:', {
    connection: connectionOk,
    tablesExist: exists,
    missingTables: missing,
    dataCounts: counts
  });

  // Recommendations
  if (missing.length > 0) {
    console.log('💡 Recommendation: Create missing tables or check table names in DATABASE_TABLES constant');
  }
  
  if (Object.values(counts).every(count => count === 0)) {
    console.log('💡 Recommendation: Tables exist but contain no data. Consider seeding the database.');
  }
}
