// src/app/attendance-management/page.tsx
'use client';

import { useState } from 'react';

export default function AttendanceManagementPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl-app font-bold text-slate-900">Attendance Management</h1>
          <p className="text-base-app text-slate-600 mt-1">
            Track and manage student and staff attendance records
          </p>
        </div>
        <button className="btn-primary">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
          Mark Attendance
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: '📊' },
            { id: 'daily', label: 'Daily Attendance', icon: '📅' },
            { id: 'students', label: 'Student Records', icon: '👨‍🎓' },
            { id: 'staff', label: 'Staff Records', icon: '👨‍🏫' },
            { id: 'reports', label: 'Reports', icon: '📈' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm-app transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl-app font-semibold text-slate-900">Attendance Overview</h2>
            
            {/* Today's Stats */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg-app font-medium text-blue-900 mb-2">Today's Attendance</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl-app font-bold text-blue-600">85%</div>
                  <div className="text-sm-app text-blue-700">Student Attendance</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl-app font-bold text-green-600">95%</div>
                  <div className="text-sm-app text-green-700">Staff Attendance</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl-app font-bold text-orange-600">12</div>
                  <div className="text-sm-app text-orange-700">Absent Students</div>
                </div>
              </div>
            </div>

            {/* Weekly Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Average Attendance', value: '87%', change: '+2%', color: 'blue' },
                { title: 'Present Today', value: '425', change: '+15', color: 'green' },
                { title: 'Absent Today', value: '25', change: '-5', color: 'red' },
                { title: 'Late Arrivals', value: '8', change: '+3', color: 'orange' },
              ].map((stat, index) => (
                <div key={index} className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm-app font-medium text-slate-600">{stat.title}</p>
                      <p className="text-2xl-app font-bold text-slate-900">{stat.value}</p>
                    </div>
                    <div className={`text-sm-app font-medium ${
                      stat.change.startsWith('+') ? 'text-green-600' : 
                      stat.change.startsWith('-') ? 'text-red-600' : 'text-slate-500'
                    }`}>
                      {stat.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Recent Activity */}
            <div>
              <h3 className="text-lg-app font-medium text-slate-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {[
                  'Class 10-A attendance marked - 28/30 present',
                  'Late arrival recorded for John Smith (Class 9-B)',
                  'Staff meeting attendance - 15/18 present',
                ].map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3 text-sm-app text-slate-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>{activity}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'daily' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📅</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Daily Attendance</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Mark daily attendance for classes and view today's records
            </p>
            <button className="btn-primary">Mark Attendance</button>
          </div>
        )}

        {activeTab === 'students' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👨‍🎓</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Student Attendance Records</h3>
            <p className="text-base-app text-slate-600 mb-6">
              View individual student attendance history and patterns
            </p>
            <button className="btn-primary">View Records</button>
          </div>
        )}

        {activeTab === 'staff' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👨‍🏫</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Staff Attendance Records</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Track staff attendance and manage leave requests
            </p>
            <button className="btn-primary">View Staff Records</button>
          </div>
        )}

        {activeTab === 'reports' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📈</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Attendance Reports</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Generate detailed attendance reports and analytics
            </p>
            <button className="btn-primary">Generate Report</button>
          </div>
        )}
      </div>
    </div>
  );
}
