// src/constants/database.ts
/**
 * Centralized database schema definitions and constants
 * Single source of truth for all Supabase table names, column names, and schema definitions
 */

/**
 * Database table names - centralized configuration
 */
export const DATABASE_TABLES = {
  // Core tables
  STUDENTS: 'students',
  GUARDIANS: 'guardians',
  ACADEMIC_RECORDS: 'academic_records',
  DOCUMENTS: 'documents',
  
  // Master data tables
  CLASSES: 'classes',
  SECTIONS: 'sections',
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations',
  
  // System tables
  PROFILES: 'profiles',
  ENROLLMENT_SESSIONS: 'enrollment_sessions',
  AUDIT_LOGS: 'audit_logs',
  SETTINGS: 'settings',
  
  // Future expansion tables
  TEACHERS: 'teachers',
  SUBJECTS: 'subjects',
  ENROLLMENTS: 'enrollments',
  ATTENDANCE: 'attendance',
  GRADES: 'grades',
  FEES: 'fees',
  EXAMS: 'exams',
  ANNOUNCEMENTS: 'announcements',
  LIBRARY_BOOKS: 'library_books',
  LIBRARY_ISSUES: 'library_issues'
} as const;

/**
 * Database column names for type safety and consistency
 */
export const DATABASE_COLUMNS = {
  // Common columns
  COMMON: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    IS_ACTIVE: 'is_active'
  },
  
  // Student table columns
  STUDENTS: {
    ID: 'id',
    FIRST_NAME: 'first_name',
    LAST_NAME: 'last_name',
    DATE_OF_BIRTH: 'date_of_birth',
    GENDER: 'gender',
    EMAIL: 'email',
    PHONE_NUMBER: 'phone_number',
    ADDRESS: 'address',
    BLOOD_GROUP: 'blood_group',
    NATIONALITY: 'nationality',
    RELIGION: 'religion',
    GUARDIAN_NAME: 'guardian_name',
    GUARDIAN_RELATION_ID: 'guardian_relation_id',
    GUARDIAN_PHONE: 'guardian_phone',
    GUARDIAN_EMAIL: 'guardian_email',
    GUARDIAN_ADDRESS: 'guardian_address',
    EMERGENCY_CONTACT: 'emergency_contact',
    CLASS_ID: 'class_id',
    SECTION_ID: 'section_id',
    ROLL_NUMBER: 'roll_number',
    ACADEMIC_YEAR_ID: 'academic_year_id',
    PREVIOUS_SCHOOL: 'previous_school',
    BIRTH_CERTIFICATE_URL: 'birth_certificate_url',
    PREVIOUS_RECORDS_URL: 'previous_records_url',
    MEDICAL_RECORDS_URL: 'medical_records_url',
    PHOTOGRAPH_URL: 'photograph_url',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Guardian table columns
  GUARDIANS: {
    ID: 'id',
    STUDENT_ID: 'student_id',
    NAME: 'name',
    RELATION_ID: 'relation_id',
    PHONE: 'phone',
    EMAIL: 'email',
    ADDRESS: 'address',
    EMERGENCY_CONTACT: 'emergency_contact',
    OCCUPATION: 'occupation',
    WORK_ADDRESS: 'work_address',
    WORK_PHONE: 'work_phone',
    IS_PRIMARY: 'is_primary',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Academic records table columns
  ACADEMIC_RECORDS: {
    ID: 'id',
    STUDENT_ID: 'student_id',
    CLASS_ID: 'class_id',
    SECTION_ID: 'section_id',
    ACADEMIC_YEAR_ID: 'academic_year_id',
    ROLL_NUMBER: 'roll_number',
    ADMISSION_DATE: 'admission_date',
    PREVIOUS_SCHOOL: 'previous_school',
    PREVIOUS_CLASS: 'previous_class',
    PREVIOUS_PERCENTAGE: 'previous_percentage',
    TRANSFER_CERTIFICATE_NUMBER: 'transfer_certificate_number',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Documents table columns
  DOCUMENTS: {
    ID: 'id',
    STUDENT_ID: 'student_id',
    TYPE: 'type',
    FILE_NAME: 'file_name',
    ORIGINAL_NAME: 'original_name',
    FILE_PATH: 'file_path',
    FILE_URL: 'file_url',
    FILE_SIZE: 'file_size',
    MIME_TYPE: 'mime_type',
    UPLOADED_AT: 'uploaded_at',
    UPLOADED_BY: 'uploaded_by',
    IS_REQUIRED: 'is_required',
    STATUS: 'status',
    NOTES: 'notes',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Classes table columns
  CLASSES: {
    ID: 'id',
    NAME: 'name',
    DESCRIPTION: 'description',
    GRADE_LEVEL: 'grade_level',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Sections table columns
  SECTIONS: {
    ID: 'id',
    NAME: 'name',
    CLASS_ID: 'class_id',
    MAX_CAPACITY: 'max_capacity',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Academic years table columns
  ACADEMIC_YEARS: {
    ID: 'id',
    YEAR: 'year',
    START_DATE: 'start_date',
    END_DATE: 'end_date',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  
  // Guardian relations table columns
  GUARDIAN_RELATIONS: {
    ID: 'id',
    NAME: 'name',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  }
} as const;

/**
 * Database configuration constants
 */
export const DATABASE_CONFIG = {
  // Schema settings
  SCHEMA: 'public',
  
  // Pagination settings
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // File upload settings
  STORAGE_BUCKET: 'student-documents',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  
  // Connection settings
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  QUERY_TIMEOUT: 60000, // 60 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Session settings
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  
  // Validation settings
  MIN_STUDENT_AGE: 3,
  MAX_STUDENT_AGE: 25,
  MAX_CLASS_CAPACITY: 50,
  
  // Document settings
  REQUIRED_DOCUMENTS: ['birth_certificate', 'photograph'] as const,
  OPTIONAL_DOCUMENTS: ['previous_records', 'medical_records', 'guardian_id', 'address_proof'] as const,
  
  // Status enums
  DOCUMENT_STATUS: {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected'
  } as const,
  
  ENROLLMENT_STATUS: {
    DRAFT: 'draft',
    IN_PROGRESS: 'in_progress',
    PENDING_REVIEW: 'pending_review',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    COMPLETED: 'completed'
  } as const,
  
  GENDER_OPTIONS: {
    MALE: 'male',
    FEMALE: 'female',
    OTHER: 'other'
  } as const
} as const;

/**
 * TypeScript interfaces for database tables
 */

// Base interface for all database entities
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

// Student entity interface
export interface StudentEntity extends BaseEntity {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: typeof DATABASE_CONFIG.GENDER_OPTIONS[keyof typeof DATABASE_CONFIG.GENDER_OPTIONS];
  email?: string | null;
  phone_number?: string | null;
  address?: string | null;
  blood_group?: string | null;
  nationality?: string | null;
  religion?: string | null;
  guardian_name: string;
  guardian_relation_id: string;
  guardian_phone: string;
  guardian_email?: string | null;
  guardian_address?: string | null;
  emergency_contact?: string | null;
  class_id: string;
  section_id: string;
  roll_number: string;
  academic_year_id: string;
  previous_school?: string | null;
  birth_certificate_url?: string | null;
  previous_records_url?: string | null;
  medical_records_url?: string | null;
  photograph_url?: string | null;
}

// Guardian entity interface
export interface GuardianEntity extends BaseEntity {
  student_id: string;
  name: string;
  relation_id: string;
  phone: string;
  email?: string | null;
  address?: string | null;
  emergency_contact?: string | null;
  occupation?: string | null;
  work_address?: string | null;
  work_phone?: string | null;
  is_primary: boolean;
}

// Academic record entity interface
export interface AcademicRecordEntity extends BaseEntity {
  student_id: string;
  class_id: string;
  section_id: string;
  academic_year_id: string;
  roll_number: string;
  admission_date: string;
  previous_school?: string | null;
  previous_class?: string | null;
  previous_percentage?: number | null;
  transfer_certificate_number?: string | null;
}

// Document entity interface
export interface DocumentEntity extends BaseEntity {
  student_id: string;
  type: string;
  file_name: string;
  original_name: string;
  file_path: string;
  file_url: string;
  file_size: number;
  mime_type: string;
  uploaded_at: string;
  uploaded_by?: string | null;
  is_required: boolean;
  status: typeof DATABASE_CONFIG.DOCUMENT_STATUS[keyof typeof DATABASE_CONFIG.DOCUMENT_STATUS];
  notes?: string | null;
}

// Class entity interface
export interface ClassEntity extends BaseEntity {
  name: string;
  description?: string | null;
  grade_level: number;
}

// Section entity interface
export interface SectionEntity extends BaseEntity {
  name: string;
  class_id: string;
  max_capacity: number;
}

// Academic year entity interface
export interface AcademicYearEntity extends BaseEntity {
  year: string;
  start_date: string;
  end_date: string;
}

// Guardian relation entity interface
export interface GuardianRelationEntity extends BaseEntity {
  name: string;
}

/**
 * Insert and update type definitions
 */
export type StudentInsert = Omit<StudentEntity, 'id' | 'created_at' | 'updated_at'>;
export type StudentUpdate = Partial<Omit<StudentEntity, 'id' | 'created_at'>>;

export type GuardianInsert = Omit<GuardianEntity, 'id' | 'created_at' | 'updated_at'>;
export type GuardianUpdate = Partial<Omit<GuardianEntity, 'id' | 'created_at'>>;

export type AcademicRecordInsert = Omit<AcademicRecordEntity, 'id' | 'created_at' | 'updated_at'>;
export type AcademicRecordUpdate = Partial<Omit<AcademicRecordEntity, 'id' | 'created_at'>>;

export type DocumentInsert = Omit<DocumentEntity, 'id' | 'created_at' | 'updated_at'>;
export type DocumentUpdate = Partial<Omit<DocumentEntity, 'id' | 'created_at'>>;

/**
 * Validation schemas and default values
 */
export const VALIDATION_RULES = {
  STUDENT: {
    FIRST_NAME: { required: true, minLength: 2, maxLength: 50, pattern: /^[a-zA-Z\s]+$/ },
    LAST_NAME: { required: true, minLength: 2, maxLength: 50, pattern: /^[a-zA-Z\s]+$/ },
    EMAIL: { required: false, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    PHONE_NUMBER: { required: false, pattern: /^[\+]?[1-9][\d]{0,15}$/ },
    DATE_OF_BIRTH: { required: true, minAge: DATABASE_CONFIG.MIN_STUDENT_AGE, maxAge: DATABASE_CONFIG.MAX_STUDENT_AGE }
  },
  GUARDIAN: {
    NAME: { required: true, minLength: 2, maxLength: 100, pattern: /^[a-zA-Z\s]+$/ },
    PHONE: { required: true, pattern: /^[\+]?[1-9][\d]{0,15}$/ },
    EMAIL: { required: false, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }
  },
  ACADEMIC_RECORD: {
    ROLL_NUMBER: { required: true, minLength: 1, maxLength: 20, pattern: /^[A-Za-z0-9]+$/ },
    PREVIOUS_PERCENTAGE: { required: false, min: 0, max: 100 }
  },
  DOCUMENT: {
    MAX_FILE_SIZE: DATABASE_CONFIG.MAX_FILE_SIZE,
    ALLOWED_TYPES: DATABASE_CONFIG.ALLOWED_FILE_TYPES
  }
} as const;

/**
 * Default values for entities
 */
export const DEFAULT_VALUES = {
  STUDENT: {
    is_active: true,
    gender: DATABASE_CONFIG.GENDER_OPTIONS.MALE
  },
  GUARDIAN: {
    is_active: true,
    is_primary: false
  },
  ACADEMIC_RECORD: {
    is_active: true
  },
  DOCUMENT: {
    is_active: true,
    is_required: false,
    status: DATABASE_CONFIG.DOCUMENT_STATUS.PENDING
  }
} as const;

/**
 * Migration-friendly schema evolution support
 */
export const SCHEMA_VERSION = '1.0.0';

export const MIGRATION_SCRIPTS = {
  '1.0.0': {
    description: 'Initial schema setup',
    tables: Object.values(DATABASE_TABLES),
    created_at: '2024-01-01T00:00:00Z'
  }
  // Future migrations can be added here
} as const;

/**
 * Helper functions for schema management
 */
export const getTableName = (tableKey: keyof typeof DATABASE_TABLES): string => {
  return DATABASE_TABLES[tableKey];
};

export const getColumnName = (table: keyof typeof DATABASE_COLUMNS, column: string): string => {
  const tableColumns = DATABASE_COLUMNS[table];
  if (!tableColumns || !(column in tableColumns)) {
    throw new Error(`Column ${column} not found in table ${table}`);
  }
  return (tableColumns as any)[column];
};

export const validateEntityStructure = <T extends BaseEntity>(
  entity: T,
  requiredFields: (keyof T)[]
): boolean => {
  return requiredFields.every(field => entity[field] !== undefined && entity[field] !== null);
};

// Type exports for external use
export type DatabaseTableName = typeof DATABASE_TABLES[keyof typeof DATABASE_TABLES];
export type DatabaseColumnName = string;

// Export all entity types for internal use
export type {
  AcademicRecordEntity as AcademicRecordEntityInternal, AcademicYearEntity as AcademicYearEntityInternal, BaseEntity as BaseEntityInternal, ClassEntity as ClassEntityInternal, DocumentEntity as DocumentEntityInternal, GuardianEntity as GuardianEntityInternal, GuardianRelationEntity as GuardianRelationEntityInternal, SectionEntity as SectionEntityInternal, StudentEntity as StudentEntityInternal
};

