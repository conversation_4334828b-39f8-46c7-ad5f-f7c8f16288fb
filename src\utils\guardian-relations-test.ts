// src/utils/guardian-relations-test.ts
/**
 * Specific test utilities for guardian relations functionality
 * This helps debug and verify the guardian relations dropdown
 */

import { supabase } from '../lib/supabase';
import { DATABASE_TABLES, DATABASE_COLUMNS } from '../constants/database';
import { MasterDataService } from '../services/masterDataService';

/**
 * Test guardian relations table existence and structure
 */
export async function testGuardianRelationsTable(): Promise<{
  exists: boolean;
  structure: any[];
  error?: string;
}> {
  try {
    console.log('🔍 Testing guardian_relations table...');
    
    // Check if table exists by querying its structure
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', DATABASE_TABLES.GUARDIAN_RELATIONS)
      .order('ordinal_position');

    if (error) {
      console.error('❌ Error checking guardian_relations table structure:', error);
      return { exists: false, structure: [], error: error.message };
    }

    if (!data || data.length === 0) {
      console.warn('⚠️ guardian_relations table does not exist');
      return { exists: false, structure: [], error: 'Table does not exist' };
    }

    console.log('✅ guardian_relations table structure:', data);
    return { exists: true, structure: data };
  } catch (error) {
    console.error('❌ Error testing guardian_relations table:', error);
    return { 
      exists: false, 
      structure: [], 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Test guardian relations data retrieval
 */
export async function testGuardianRelationsData(): Promise<{
  success: boolean;
  data: any[];
  count: number;
  error?: string;
}> {
  try {
    console.log('🔍 Testing guardian relations data retrieval...');
    
    const { data, error } = await supabase
      .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
      .select(`
        ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.ID},
        ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.NAME},
        ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.IS_ACTIVE},
        ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.CREATED_AT}
      `)
      .eq(DATABASE_COLUMNS.GUARDIAN_RELATIONS.IS_ACTIVE, true)
      .order(DATABASE_COLUMNS.GUARDIAN_RELATIONS.NAME, { ascending: true });

    if (error) {
      console.error('❌ Error fetching guardian relations data:', error);
      return { success: false, data: [], count: 0, error: error.message };
    }

    console.log('✅ Guardian relations data retrieved:', data);
    return { success: true, data: data || [], count: data?.length || 0 };
  } catch (error) {
    console.error('❌ Error testing guardian relations data:', error);
    return { 
      success: false, 
      data: [], 
      count: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Test master data service guardian relations method
 */
export async function testMasterDataServiceGuardianRelations(): Promise<{
  success: boolean;
  data: any[];
  count: number;
  error?: string;
}> {
  try {
    console.log('🔍 Testing MasterDataService.getGuardianRelations()...');
    
    const data = await MasterDataService.getGuardianRelations();
    
    console.log('✅ MasterDataService guardian relations:', data);
    return { success: true, data, count: data.length };
  } catch (error) {
    console.error('❌ Error testing MasterDataService guardian relations:', error);
    return { 
      success: false, 
      data: [], 
      count: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Insert sample guardian relations data
 */
export async function insertSampleGuardianRelations(): Promise<{
  success: boolean;
  inserted: number;
  error?: string;
}> {
  try {
    console.log('🌱 Inserting sample guardian relations...');
    
    const sampleRelations = [
      { name: 'Father' },
      { name: 'Mother' },
      { name: 'Guardian' },
      { name: 'Grandfather' },
      { name: 'Grandmother' },
      { name: 'Uncle' },
      { name: 'Aunt' },
      { name: 'Brother' },
      { name: 'Sister' },
      { name: 'Other' }
    ];

    // Check if data already exists
    const { data: existing } = await supabase
      .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
      .select(DATABASE_COLUMNS.GUARDIAN_RELATIONS.ID)
      .limit(1);

    if (existing && existing.length > 0) {
      console.log('ℹ️ Guardian relations already exist, skipping insert');
      return { success: true, inserted: 0 };
    }

    const { data, error } = await supabase
      .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
      .insert(sampleRelations)
      .select();

    if (error) {
      console.error('❌ Error inserting guardian relations:', error);
      return { success: false, inserted: 0, error: error.message };
    }

    console.log(`✅ Inserted ${data?.length || 0} guardian relations`);
    return { success: true, inserted: data?.length || 0 };
  } catch (error) {
    console.error('❌ Error inserting sample guardian relations:', error);
    return { 
      success: false, 
      inserted: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Run comprehensive guardian relations test
 */
export async function runGuardianRelationsTest(): Promise<{
  tableExists: boolean;
  dataAvailable: boolean;
  serviceWorks: boolean;
  recommendations: string[];
  details: any;
}> {
  console.log('🚀 Running comprehensive guardian relations test...');
  
  const tableTest = await testGuardianRelationsTable();
  const dataTest = await testGuardianRelationsData();
  const serviceTest = await testMasterDataServiceGuardianRelations();
  
  const recommendations: string[] = [];
  
  if (!tableTest.exists) {
    recommendations.push('Create guardian_relations table using the provided SQL script');
  }
  
  if (tableTest.exists && dataTest.count === 0) {
    recommendations.push('Insert sample guardian relations data');
  }
  
  if (!serviceTest.success) {
    recommendations.push('Check MasterDataService implementation and database permissions');
  }
  
  if (tableTest.exists && dataTest.count > 0 && serviceTest.success) {
    recommendations.push('✅ Guardian relations are working correctly!');
  }

  const results = {
    tableExists: tableTest.exists,
    dataAvailable: dataTest.count > 0,
    serviceWorks: serviceTest.success,
    recommendations,
    details: {
      table: tableTest,
      data: dataTest,
      service: serviceTest
    }
  };

  console.log('📊 Guardian relations test results:', results);
  return results;
}

/**
 * Fix guardian relations issues automatically
 */
export async function fixGuardianRelationsIssues(): Promise<{
  success: boolean;
  actions: string[];
  error?: string;
}> {
  try {
    console.log('🔧 Attempting to fix guardian relations issues...');
    
    const actions: string[] = [];
    
    // Test current state
    const tableTest = await testGuardianRelationsTable();
    
    if (!tableTest.exists) {
      actions.push('❌ Table does not exist - please run the SQL setup script');
      return { success: false, actions };
    }
    
    // Check if data exists
    const dataTest = await testGuardianRelationsData();
    
    if (dataTest.count === 0) {
      console.log('🌱 No data found, inserting sample data...');
      const insertResult = await insertSampleGuardianRelations();
      
      if (insertResult.success) {
        actions.push(`✅ Inserted ${insertResult.inserted} guardian relations`);
      } else {
        actions.push(`❌ Failed to insert data: ${insertResult.error}`);
        return { success: false, actions };
      }
    } else {
      actions.push(`✅ Found ${dataTest.count} guardian relations`);
    }
    
    // Test service
    const serviceTest = await testMasterDataServiceGuardianRelations();
    
    if (serviceTest.success) {
      actions.push(`✅ MasterDataService working correctly with ${serviceTest.count} relations`);
    } else {
      actions.push(`❌ MasterDataService failed: ${serviceTest.error}`);
      return { success: false, actions };
    }
    
    actions.push('🎉 All guardian relations issues fixed!');
    return { success: true, actions };
  } catch (error) {
    console.error('❌ Error fixing guardian relations issues:', error);
    return { 
      success: false, 
      actions: ['❌ Failed to fix issues'], 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
