// src/app/dashboard/_components/dashboard-content.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import AuthModal from '../../../components/auth/auth-modal';
import { useAuth } from '../../../components/auth/auth-provider';
import PageWrapper from '../../../components/common/page-wrapper';

const DashboardContent = () => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  // Handle successful login - redirect to dashboard
  const _handleAuthSuccess = () => {
    setIsAuthModalOpen(false);
    // The useEffect will handle the redirect once user state updates
  };

  useEffect(() => {
    // Set timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      router.push('/product');
    }, 5000);

    // Only redirect if auth is fully loaded and user is definitely not authenticated
    if (!loading && (!user || !user.isAuthenticated)) {
      clearTimeout(timeoutId);
      router.push('/product');
      return;
    }

    // Clear timeout if user is authenticated
    if (!loading && user && user.isAuthenticated) {
      clearTimeout(timeoutId);
    }

    return () => clearTimeout(timeoutId);
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>

          {/* Provide escape route */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-blue-600 hover:text-blue-700 underline text-sm"
            >
              Go to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !user.isAuthenticated) {

    return (
      <>
        {/* Authentication Required Page - Product Theme */}
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="flex-grow flex items-center justify-center relative z-10 px-4">
            <div className="text-center max-w-2xl mx-auto">
              {/* Lock Icon */}
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full mb-8 shadow-2xl shadow-indigo-500/25">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V9a4 4 0 00-8 0v2m8 0V9a4 4 0 00-4-4 4 4 0 00-4 4v2" />
                </svg>
              </div>

              {/* Heading */}
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Authentication Required
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                Please sign in to access your EduPro dashboard and manage your school operations.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Sign In to Dashboard
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <button
                  onClick={() => router.push('/product')}
                  className="group px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-2xl font-semibold text-lg hover:border-indigo-500 hover:text-indigo-600 hover:bg-indigo-50 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl"
                >
                  <span className="flex items-center justify-center">
                    Return Home
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </span>
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">Secure Access</div>
                    <div className="text-xs text-slate-600">Enterprise security</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">FERPA Compliant</div>
                    <div className="text-xs text-slate-600">Data protection</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">24/7 Support</div>
                    <div className="text-xs text-slate-600">Expert assistance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
          defaultTab="login"
        />
      </>
    );
  }

  return (
    <PageWrapper>
      {/* EduPro v2.0 Announcement Banner - Compact */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg p-3 mb-4 text-white shadow-md">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="bg-white/20 rounded-md p-1.5">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-semibold">🎉 EduPro v2.0 is Live!</h3>
              <p className="text-blue-100 text-xs opacity-90">
                Discover enhanced features, blazing speed, and a refreshed interface
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-3 py-1.5 rounded-md text-xs font-semibold hover:bg-blue-50 transition-colors shadow-sm">
            Explore +
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-4">
        <h2 className="text-base font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {/* Enroll Student */}
          <div className="bg-white rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Enroll Student</h3>
            <p className="text-xs text-gray-600 mb-3">Quickly add new students to the school roster.</p>
            <button
              onClick={() => router.push('/student-management')}
              className="w-full bg-blue-600 text-white py-1.5 px-3 rounded-md text-xs font-medium hover:bg-blue-700 transition-colors"
            >
              Enroll Now →
            </button>
          </div>

          {/* Manage Timetable */}
          <div className="bg-white rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Manage Timetable</h3>
            <p className="text-xs text-gray-600 mb-3">View, create, or update class and exam schedules.</p>
            <button
              onClick={() => router.push('/academic-management')}
              className="w-full bg-green-600 text-white py-1.5 px-3 rounded-md text-xs font-medium hover:bg-green-700 transition-colors"
            >
              Open Timetable →
            </button>
          </div>

          {/* View Reports */}
          <div className="bg-white rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">View Reports</h3>
            <p className="text-xs text-gray-600 mb-3">Access academic, attendance, or financial reports.</p>
            <button
              onClick={() => router.push('/reports')}
              className="w-full bg-orange-600 text-white py-1.5 px-3 rounded-md text-xs font-medium hover:bg-orange-700 transition-colors"
            >
              Generate Reports →
            </button>
          </div>

          {/* Get Support */}
          <div className="bg-white rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Get Support</h3>
            <p className="text-xs text-gray-600 mb-3">Find help documentation, FAQs, or contact support.</p>
            <button
              onClick={() => router.push('/resources')}
              className="w-full bg-purple-600 text-white py-1.5 px-3 rounded-md text-xs font-medium hover:bg-purple-700 transition-colors"
            >
              Access Help →
            </button>
          </div>
        </div>
      </div>

      {/* School At-a-Glance */}
      <div className="mb-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 shadow-lg border border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">School At-a-Glance</h2>
          <div className="flex bg-white rounded-lg p-1 shadow-sm border border-gray-200">
            <button className="px-2 py-1 text-xs bg-blue-600 text-white rounded-md font-medium transition-all">
              👥 Students
            </button>
            <button className="px-2 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded-md transition-all">
              👨‍🏫 Staff
            </button>
            <button className="px-2 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded-md transition-all">
              📅 Events
            </button>
            <button className="px-2 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded-md transition-all">
              📊 Attendance
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          {/* Total Students */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <p className="text-xs text-gray-600">Total Students</p>
                <p className="text-lg font-bold text-gray-900">1,250</p>
              </div>
            </div>
          </div>

          {/* Average Class Size */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <p className="text-xs text-gray-600">Average Class Size</p>
                <p className="text-lg font-bold text-gray-900">28</p>
              </div>
            </div>
          </div>

          {/* Gender Ratio */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <p className="text-xs text-gray-600">Gender Ratio</p>
                <p className="text-sm font-bold text-gray-900">55% M / 45% F</p>
              </div>
            </div>
          </div>

          {/* Scholarship Holders */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <div>
                <p className="text-xs text-gray-600">Scholarship Holders</p>
                <p className="text-lg font-bold text-gray-900">125</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* New Enrollments by Grade (Last 30 Days) */}
      <div className="mb-4">
        <h2 className="text-base font-semibold text-gray-900 mb-3">New Enrollments by Grade (Last 30 Days)</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          {/* Grade 9 */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="text-center">
              <p className="text-xs text-gray-600 mb-1">Grade 9</p>
              <p className="text-lg font-bold text-blue-600 mb-1">15</p>
              <p className="text-xs text-gray-500">students</p>
            </div>
          </div>

          {/* Grade 10 */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="text-center">
              <p className="text-xs text-gray-600 mb-1">Grade 10</p>
              <p className="text-lg font-bold text-blue-600 mb-1">12</p>
              <p className="text-xs text-gray-500">students</p>
            </div>
          </div>

          {/* Grade 11 */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="text-center">
              <p className="text-xs text-gray-600 mb-1">Grade 11</p>
              <p className="text-lg font-bold text-blue-600 mb-1">10</p>
              <p className="text-xs text-gray-500">students</p>
            </div>
          </div>

          {/* Grade 12 */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="text-center">
              <p className="text-xs text-gray-600 mb-1">Grade 12</p>
              <p className="text-lg font-bold text-blue-600 mb-1">13</p>
              <p className="text-xs text-gray-500">students</p>
            </div>
          </div>
        </div>
      </div>

      {/* Announcements */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">Announcements</h2>
          <button className="text-blue-600 text-xs font-medium hover:text-blue-700">
            View All →
          </button>
        </div>

        <div className="space-y-3">
          {/* Mid-Term Exam Schedule */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 bg-orange-100 rounded-md flex items-center justify-center flex-shrink-0">
                <span className="text-orange-600 text-xs">📝</span>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1 text-sm">Mid-Term Exam Schedule</h3>
                <p className="text-xs text-gray-600">The schedule for the upcoming mid-term examinations has been finalized and is now available.</p>
              </div>
            </div>
          </div>

          {/* Annual Cultural Fest */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center flex-shrink-0">
                <span className="text-green-600 text-xs">🎭</span>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1 text-sm">Annual Cultural Fest "Utsav"</h3>
                <p className="text-xs text-gray-600">Get ready for Utsav 2023! Auditions for various cultural events are now open.</p>
              </div>
            </div>
          </div>

          {/* Community Service Drive */}
          <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 bg-purple-100 rounded-md flex items-center justify-center flex-shrink-0">
                <span className="text-purple-600 text-xs">🤝</span>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1 text-sm">Community Service Drive</h3>
                <p className="text-xs text-gray-600">Join us for the upcoming community service drive to make a positive impact in our local area.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default DashboardContent;
