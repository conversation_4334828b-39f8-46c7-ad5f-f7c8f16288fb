// src/app/dashboard/_components/dashboard-content.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import AuthModal from '../../../components/auth/auth-modal';
import { useAuth } from '../../../components/auth/auth-provider';
import PageWrapper from '../../../components/common/page-wrapper';

const DashboardContent = () => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'staff' | 'events'>('overview');

  // Handle successful login - redirect to dashboard
  const _handleAuthSuccess = () => {
    setIsAuthModalOpen(false);
    // The useEffect will handle the redirect once user state updates
  };

  useEffect(() => {
    // Set timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      router.push('/product');
    }, 5000);

    // Only redirect if auth is fully loaded and user is definitely not authenticated
    if (!loading && (!user || !user.isAuthenticated)) {
      clearTimeout(timeoutId);
      router.push('/product');
      return;
    }

    // Clear timeout if user is authenticated
    if (!loading && user && user.isAuthenticated) {
      clearTimeout(timeoutId);
    }

    return () => clearTimeout(timeoutId);
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>

          {/* Provide escape route */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-blue-600 hover:text-blue-700 underline text-sm"
            >
              Go to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !user.isAuthenticated) {

    return (
      <>
        {/* Authentication Required Page - Product Theme */}
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="flex-grow flex items-center justify-center relative z-10 px-4">
            <div className="text-center max-w-2xl mx-auto">
              {/* Lock Icon */}
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full mb-8 shadow-2xl shadow-indigo-500/25">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V9a4 4 0 00-8 0v2m8 0V9a4 4 0 00-4-4 4 4 0 00-4 4v2" />
                </svg>
              </div>

              {/* Heading */}
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Authentication Required
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                Please sign in to access your EduPro dashboard and manage your school operations.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Sign In to Dashboard
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <button
                  onClick={() => router.push('/product')}
                  className="group px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-2xl font-semibold text-lg hover:border-indigo-500 hover:text-indigo-600 hover:bg-indigo-50 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl"
                >
                  <span className="flex items-center justify-center">
                    Return Home
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </span>
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">Secure Access</div>
                    <div className="text-xs text-slate-600">Enterprise security</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">FERPA Compliant</div>
                    <div className="text-xs text-slate-600">Data protection</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">24/7 Support</div>
                    <div className="text-xs text-slate-600">Expert assistance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
          defaultTab="login"
        />
      </>
    );
  }

  return (
    <PageWrapper>
      {/* EduPro v2.0 Announcement Banner - Compact */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg p-3 mb-4 text-white shadow-md">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="bg-white/20 rounded-md p-1.5">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-semibold">🎉 EduPro v2.0 is Live!</h3>
              <p className="text-blue-100 text-xs opacity-90">
                Discover enhanced features, blazing speed, and a refreshed interface
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-3 py-1.5 rounded-md text-xs font-semibold hover:bg-blue-50 transition-colors shadow-sm">
            Explore +
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-4">
        <h2 className="text-base font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {/* Enroll Student */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-blue-100/50 hover:shadow-xl hover:shadow-blue-200/60 transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-xs">Enroll Student</h3>
            <p className="text-xs text-gray-600 mb-2">Quickly add new students to the school roster.</p>
            <button
              onClick={() => router.push('/student-management')}
              className="w-full bg-blue-600 text-white py-1 px-2 rounded-md text-xs font-medium hover:bg-blue-700 transition-colors"
            >
              Enroll Now →
            </button>
          </div>

          {/* Manage Timetable */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-green-100/50 hover:shadow-xl hover:shadow-green-200/60 transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-xs">Manage Timetable</h3>
            <p className="text-xs text-gray-600 mb-2">View, create, or update class and exam schedules.</p>
            <button
              onClick={() => router.push('/academic-management')}
              className="w-full bg-green-600 text-white py-1 px-2 rounded-md text-xs font-medium hover:bg-green-700 transition-colors"
            >
              Open Timetable →
            </button>
          </div>

          {/* View Reports */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-orange-100/50 hover:shadow-xl hover:shadow-orange-200/60 transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-xs">View Reports</h3>
            <p className="text-xs text-gray-600 mb-2">Access academic, attendance, or financial reports.</p>
            <button
              onClick={() => router.push('/reports')}
              className="w-full bg-orange-600 text-white py-1 px-2 rounded-md text-xs font-medium hover:bg-orange-700 transition-colors"
            >
              Generate Reports →
            </button>
          </div>

          {/* Get Support */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-purple-100/50 hover:shadow-xl hover:shadow-purple-200/60 transition-all duration-300 border border-gray-100 hover:-translate-y-1">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-xs">Get Support</h3>
            <p className="text-xs text-gray-600 mb-2">Find help documentation, FAQs, or contact support.</p>
            <button
              onClick={() => router.push('/resources')}
              className="w-full bg-purple-600 text-white py-1 px-2 rounded-md text-xs font-medium hover:bg-purple-700 transition-colors"
            >
              Access Help →
            </button>
          </div>
        </div>
      </div>

      {/* School Analytics Dashboard - Redesigned */}
      <div className="mb-4 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40 rounded-xl p-5 shadow-xl border border-slate-200/60 backdrop-blur-sm relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-40 h-40 bg-blue-500 rounded-full -translate-x-20 -translate-y-20"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-purple-500 rounded-full translate-x-16 translate-y-16"></div>
        </div>

        {/* Header with Modern Toggle */}
        <div className="flex items-center justify-between mb-6 relative z-10">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
            <h2 className="text-lg font-bold text-gray-900">School Analytics</h2>
            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full font-medium">Live Data</span>
          </div>
          <div className="flex bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-md border border-white/40">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all ${
                activeTab === 'overview'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-white/60'
              }`}
            >
              📊 Overview
            </button>
            <button
              onClick={() => setActiveTab('staff')}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all ${
                activeTab === 'staff'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-white/60'
              }`}
            >
              👨‍🏫 Staff
            </button>
            <button
              onClick={() => setActiveTab('events')}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all ${
                activeTab === 'events'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-white/60'
              }`}
            >
              📅 Events
            </button>
          </div>
        </div>

        {/* Dashboard Charts Section */}
        <div className="relative z-10 mb-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">

            {/* Chart 1: Student Distribution Donut Chart */}
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 hover:shadow-lg transition-all">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-800">Student Distribution</h3>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full font-medium">1,250 Total</span>
              </div>

              {/* Donut Chart Visualization */}
              <div className="relative w-32 h-32 mx-auto mb-4">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                  {/* Background Circle */}
                  <circle cx="60" cy="60" r="50" fill="none" stroke="#e5e7eb" strokeWidth="12"/>

                  {/* Grade 9 - Blue (30%) */}
                  <circle cx="60" cy="60" r="50" fill="none" stroke="#3b82f6" strokeWidth="12"
                    strokeDasharray="94.2 314" strokeDashoffset="0" className="transition-all duration-1000"/>

                  {/* Grade 10 - Green (25%) */}
                  <circle cx="60" cy="60" r="50" fill="none" stroke="#10b981" strokeWidth="12"
                    strokeDasharray="78.5 314" strokeDashoffset="-94.2" className="transition-all duration-1000"/>

                  {/* Grade 11 - Purple (25%) */}
                  <circle cx="60" cy="60" r="50" fill="none" stroke="#8b5cf6" strokeWidth="12"
                    strokeDasharray="78.5 314" strokeDashoffset="-172.7" className="transition-all duration-1000"/>

                  {/* Grade 12 - Amber (20%) */}
                  <circle cx="60" cy="60" r="50" fill="none" stroke="#f59e0b" strokeWidth="12"
                    strokeDasharray="62.8 314" strokeDashoffset="-251.2" className="transition-all duration-1000"/>
                </svg>

                {/* Center Text */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-lg font-bold text-gray-900">1,250</p>
                    <p className="text-xs text-gray-600">Students</p>
                  </div>
                </div>
              </div>

              {/* Legend */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700">Grade 9</span>
                  </div>
                  <span className="font-semibold text-gray-900">375 (30%)</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                    <span className="text-gray-700">Grade 10</span>
                  </div>
                  <span className="font-semibold text-gray-900">313 (25%)</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">Grade 11</span>
                  </div>
                  <span className="font-semibold text-gray-900">313 (25%)</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                    <span className="text-gray-700">Grade 12</span>
                  </div>
                  <span className="font-semibold text-gray-900">249 (20%)</span>
                </div>
              </div>

              <p className="text-xs text-gray-600 mt-3 leading-relaxed">
                Shows the current distribution of students across all grade levels. Grade 9 has the highest enrollment, indicating strong new admissions.
              </p>
            </div>

            {/* Chart 2: Monthly Enrollment Trend Line Chart */}
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 hover:shadow-lg transition-all">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-800">Enrollment Trends</h3>
                <span className="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full font-medium">+12% Growth</span>
              </div>

              {/* Line Chart Visualization */}
              <div className="relative h-32 mb-4">
                <svg className="w-full h-32" viewBox="0 0 300 120">
                  {/* Grid Lines */}
                  <defs>
                    <pattern id="grid" width="50" height="24" patternUnits="userSpaceOnUse">
                      <path d="M 50 0 L 0 0 0 24" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
                    </pattern>
                  </defs>
                  <rect width="300" height="120" fill="url(#grid)" />

                  {/* Gradient Fill */}
                  <defs>
                    <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3"/>
                      <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.05"/>
                    </linearGradient>
                  </defs>

                  {/* Area Fill */}
                  <path d="M 20 80 L 70 70 L 120 65 L 170 55 L 220 45 L 270 35 L 270 120 L 20 120 Z"
                    fill="url(#areaGradient)" className="transition-all duration-1000"/>

                  {/* Line */}
                  <path d="M 20 80 L 70 70 L 120 65 L 170 55 L 220 45 L 270 35"
                    fill="none" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round"
                    className="transition-all duration-1000"/>

                  {/* Data Points */}
                  <circle cx="20" cy="80" r="4" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="70" cy="70" r="4" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="120" cy="65" r="4" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="170" cy="55" r="4" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="220" cy="45" r="4" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="270" cy="35" r="4" fill="#3b82f6" className="transition-all duration-1000"/>
                </svg>
              </div>

              {/* Month Labels */}
              <div className="flex justify-between text-xs text-gray-600 mb-3">
                <span>Aug</span>
                <span>Sep</span>
                <span>Oct</span>
                <span>Nov</span>
                <span>Dec</span>
                <span>Jan</span>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="text-center">
                  <p className="text-lg font-bold text-emerald-600">+50</p>
                  <p className="text-xs text-gray-600">This Month</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold text-blue-600">285</p>
                  <p className="text-xs text-gray-600">6-Month Total</p>
                </div>
              </div>

              <p className="text-xs text-gray-600 leading-relaxed">
                Monthly enrollment trends show consistent growth with peak admissions in January. The upward trajectory indicates strong school reputation and demand.
              </p>
            </div>

            {/* Chart 3: Performance Metrics Bar Chart */}
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 hover:shadow-lg transition-all">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-800">Key Metrics</h3>
                <span className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded-full font-medium">Excellent</span>
              </div>

              {/* Bar Chart Visualization */}
              <div className="space-y-4 mb-4">
                {/* Attendance Rate */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700">Attendance Rate</span>
                    <span className="text-sm font-bold text-emerald-600">94%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-emerald-400 to-emerald-500 h-2 rounded-full transition-all duration-1000"
                      style={{width: '94%'}}></div>
                  </div>
                </div>

                {/* Academic Performance */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700">Academic Performance</span>
                    <span className="text-sm font-bold text-blue-600">87%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-1000"
                      style={{width: '87%'}}></div>
                  </div>
                </div>

                {/* Teacher Satisfaction */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700">Teacher Satisfaction</span>
                    <span className="text-sm font-bold text-purple-600">91%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full transition-all duration-1000"
                      style={{width: '91%'}}></div>
                  </div>
                </div>

                {/* Parent Engagement */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700">Parent Engagement</span>
                    <span className="text-sm font-bold text-amber-600">78%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-amber-400 to-amber-500 h-2 rounded-full transition-all duration-1000"
                      style={{width: '78%'}}></div>
                  </div>
                </div>
              </div>

              {/* Summary Stats */}
              <div className="grid grid-cols-2 gap-2 mb-3">
                <div className="text-center p-2 bg-emerald-50 rounded-lg">
                  <p className="text-sm font-bold text-emerald-600">A+</p>
                  <p className="text-xs text-gray-600">Overall Grade</p>
                </div>
                <div className="text-center p-2 bg-blue-50 rounded-lg">
                  <p className="text-sm font-bold text-blue-600">Top 5%</p>
                  <p className="text-xs text-gray-600">District Rank</p>
                </div>
              </div>

              <p className="text-xs text-gray-600 leading-relaxed">
                Comprehensive performance indicators across key areas. High attendance and academic scores reflect excellent school management and student engagement.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Inspirational Quote - Compact Design */}
      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-700 rounded-xl p-4 shadow-xl border border-indigo-200/20 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
        <div className="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full -translate-y-12 translate-x-12"></div>
        <div className="absolute bottom-0 left-0 w-20 h-20 bg-white/5 rounded-full translate-y-10 -translate-x-10"></div>

        {/* Content */}
        <div className="relative z-10">
          <div className="flex items-start space-x-3">
            <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center flex-shrink-0">
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
              </svg>
            </div>
            <div className="flex-1">
              <blockquote className="text-white/95 text-sm font-medium leading-relaxed mb-2">
                "Education is the most powerful weapon which you can use to change the world. Every school is a beacon of hope, shaping minds that will build a better tomorrow for our nation."
              </blockquote>

              {/* Author - Right Aligned */}
              <div className="flex items-center justify-between">
                <p className="text-white/80 text-xs flex items-center">
                  🌟 Building tomorrow's leaders, one student at a time
                </p>
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">NM</span>
                  </div>
                  <div className="text-right">
                    <p className="text-white/90 text-xs font-semibold">Nelson Mandela</p>
                    <p className="text-white/70 text-xs">Former President of South Africa</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default DashboardContent;
