// src/app/dashboard/_components/dashboard-content.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import AuthModal from '../../../components/auth/auth-modal';
import { useAuth } from '../../../components/auth/auth-provider';
import PageWrapper from '../../../components/common/page-wrapper';

const DashboardContent = () => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'staff' | 'events'>('overview');

  // Data for different tabs
  const getChartData = () => {
    switch (activeTab) {
      case 'overview':
        return {
          totalStudents: 1250,
          distribution: [
            { grade: 'Grade 9', count: 375, percentage: 30, color: '#3b82f6' },
            { grade: 'Grade 10', count: 313, percentage: 25, color: '#10b981' },
            { grade: 'Grade 11', count: 313, percentage: 25, color: '#8b5cf6' },
            { grade: 'Grade 12', count: 249, percentage: 20, color: '#f59e0b' }
          ],
          enrollmentGrowth: '+12%',
          monthlyEnrollments: [45, 52, 48, 65, 72, 85],
          metrics: {
            attendance: 94,
            academic: 87,
            teacherSat: 91,
            parentEng: 78
          }
        };
      case 'staff':
        return {
          totalStudents: 85,
          distribution: [
            { grade: 'Teachers', count: 45, percentage: 53, color: '#3b82f6' },
            { grade: 'Admin Staff', count: 15, percentage: 18, color: '#10b981' },
            { grade: 'Support Staff', count: 20, percentage: 23, color: '#8b5cf6' },
            { grade: 'Part-time', count: 5, percentage: 6, color: '#f59e0b' }
          ],
          enrollmentGrowth: '+8%',
          monthlyEnrollments: [2, 3, 1, 4, 2, 3],
          metrics: {
            attendance: 96,
            academic: 92,
            teacherSat: 88,
            parentEng: 85
          }
        };
      case 'events':
        return {
          totalStudents: 24,
          distribution: [
            { grade: 'Academic', count: 8, percentage: 33, color: '#3b82f6' },
            { grade: 'Sports', count: 6, percentage: 25, color: '#10b981' },
            { grade: 'Cultural', count: 7, percentage: 29, color: '#8b5cf6' },
            { grade: 'Community', count: 3, percentage: 13, color: '#f59e0b' }
          ],
          enrollmentGrowth: '+25%',
          monthlyEnrollments: [3, 4, 2, 5, 6, 4],
          metrics: {
            attendance: 89,
            academic: 95,
            teacherSat: 93,
            parentEng: 82
          }
        };
      default:
        return {
          totalStudents: 1250,
          distribution: [],
          enrollmentGrowth: '+12%',
          monthlyEnrollments: [],
          metrics: { attendance: 0, academic: 0, teacherSat: 0, parentEng: 0 }
        };
    }
  };

  const chartData = getChartData();

  // Handle successful login - redirect to dashboard
  const _handleAuthSuccess = () => {
    setIsAuthModalOpen(false);
    // The useEffect will handle the redirect once user state updates
  };

  useEffect(() => {
    // Set timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      router.push('/product');
    }, 5000);

    // Only redirect if auth is fully loaded and user is definitely not authenticated
    if (!loading && (!user || !user.isAuthenticated)) {
      clearTimeout(timeoutId);
      router.push('/product');
      return;
    }

    // Clear timeout if user is authenticated
    if (!loading && user && user.isAuthenticated) {
      clearTimeout(timeoutId);
    }

    return () => clearTimeout(timeoutId);
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>

          {/* Provide escape route */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-blue-600 hover:text-blue-700 underline text-sm"
            >
              Go to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !user.isAuthenticated) {

    return (
      <>
        {/* Authentication Required Page - Product Theme */}
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="flex-grow flex items-center justify-center relative z-10 px-4">
            <div className="text-center max-w-2xl mx-auto">
              {/* Lock Icon */}
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full mb-8 shadow-2xl shadow-indigo-500/25">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V9a4 4 0 00-8 0v2m8 0V9a4 4 0 00-4-4 4 4 0 00-4 4v2" />
                </svg>
              </div>

              {/* Heading */}
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Authentication Required
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                Please sign in to access your EduPro dashboard and manage your school operations.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Sign In to Dashboard
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <button
                  onClick={() => router.push('/product')}
                  className="group px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-2xl font-semibold text-lg hover:border-indigo-500 hover:text-indigo-600 hover:bg-indigo-50 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl"
                >
                  <span className="flex items-center justify-center">
                    Return Home
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </span>
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">Secure Access</div>
                    <div className="text-xs text-slate-600">Enterprise security</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">FERPA Compliant</div>
                    <div className="text-xs text-slate-600">Data protection</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">24/7 Support</div>
                    <div className="text-xs text-slate-600">Expert assistance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
          defaultTab="login"
        />
      </>
    );
  }

  return (
    <PageWrapper>
      {/* EduPro v2.0 Announcement Banner - Compact */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg p-2.5 mb-3 text-white shadow-lg shadow-indigo-200/40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="bg-white/20 rounded-md p-1">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-semibold">🎉 EduPro v2.0 is Live!</h3>
              <p className="text-blue-100 text-sm opacity-90">
                Discover enhanced features, blazing speed, and a refreshed interface
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-3 py-1.5 rounded-md text-sm font-semibold hover:bg-blue-50 transition-colors shadow-md shadow-white/20">
            Explore +
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-3">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 max-w-4xl">
          {/* Enroll Student */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-blue-200/40 hover:shadow-xl hover:shadow-blue-300/50 transition-all duration-300 border border-gray-100 hover:-translate-y-1 max-w-xs">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Enroll Student</h3>
            <p className="text-sm text-gray-600 mb-2">Quickly add new students to the school roster.</p>
            <button
              onClick={() => router.push('/student-management')}
              className="w-full bg-blue-600 text-white py-1.5 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors shadow-md shadow-blue-200/30"
            >
              Enroll Now →
            </button>
          </div>

          {/* Manage Timetable */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-green-200/40 hover:shadow-xl hover:shadow-green-300/50 transition-all duration-300 border border-gray-100 hover:-translate-y-1 max-w-xs">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Manage Timetable</h3>
            <p className="text-sm text-gray-600 mb-2">View, create, or update class and exam schedules.</p>
            <button
              onClick={() => router.push('/academic-management')}
              className="w-full bg-green-600 text-white py-1.5 px-3 rounded-md text-sm font-medium hover:bg-green-700 transition-colors shadow-md shadow-green-200/30"
            >
              Open Timetable →
            </button>
          </div>

          {/* View Reports */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-orange-200/40 hover:shadow-xl hover:shadow-orange-300/50 transition-all duration-300 border border-gray-100 hover:-translate-y-1 max-w-xs">
            <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">View Reports</h3>
            <p className="text-sm text-gray-600 mb-2">Access academic, attendance, or financial reports.</p>
            <button
              onClick={() => router.push('/reports')}
              className="w-full bg-orange-600 text-white py-1.5 px-3 rounded-md text-sm font-medium hover:bg-orange-700 transition-colors shadow-md shadow-orange-200/30"
            >
              Generate Reports →
            </button>
          </div>

          {/* Get Support */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-purple-200/40 hover:shadow-xl hover:shadow-purple-300/50 transition-all duration-300 border border-gray-100 hover:-translate-y-1 max-w-xs">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Get Support</h3>
            <p className="text-sm text-gray-600 mb-2">Find help documentation, FAQs, or contact support.</p>
            <button
              onClick={() => router.push('/resources')}
              className="w-full bg-purple-600 text-white py-1.5 px-3 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors shadow-md shadow-purple-200/30"
            >
              Access Help →
            </button>
          </div>
        </div>
      </div>

      {/* School Analytics Dashboard - Redesigned */}
      <div className="mb-3 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40 rounded-xl p-4 shadow-xl shadow-slate-200/50 border border-slate-200/60 backdrop-blur-sm relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-32 h-32 bg-blue-500 rounded-full -translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-purple-500 rounded-full translate-x-12 translate-y-12"></div>
        </div>

        {/* Header with Modern Toggle */}
        <div className="flex items-center justify-between mb-4 relative z-10">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
            <h2 className="text-lg font-bold text-gray-900">School Analytics</h2>
            <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded-full font-medium">Live Data</span>
          </div>
          <div className="flex bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-md border border-white/40">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all ${
                activeTab === 'overview'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-white/60'
              }`}
            >
              📊 Overview
            </button>
            <button
              onClick={() => setActiveTab('staff')}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all ${
                activeTab === 'staff'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-white/60'
              }`}
            >
              👨‍🏫 Staff
            </button>
            <button
              onClick={() => setActiveTab('events')}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all ${
                activeTab === 'events'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-white/60'
              }`}
            >
              📅 Events
            </button>
          </div>
        </div>

        {/* Dashboard Charts Section */}
        <div className="relative z-10 mb-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">

            {/* Chart 1: Dynamic Distribution Donut Chart */}
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-3 border border-white/50 shadow-lg shadow-blue-200/30 hover:shadow-xl hover:shadow-blue-300/40 transition-all">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-semibold text-gray-800">
                  {activeTab === 'overview' ? 'Student Distribution' :
                   activeTab === 'staff' ? 'Staff Distribution' : 'Event Categories'}
                </h3>
                <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded-full font-medium">
                  {chartData.totalStudents} Total
                </span>
              </div>

              {/* Donut Chart Visualization */}
              <div className="relative w-28 h-28 mx-auto mb-3">
                <svg className="w-28 h-28 transform -rotate-90" viewBox="0 0 120 120">
                  {/* Background Circle */}
                  <circle cx="60" cy="60" r="45" fill="none" stroke="#e5e7eb" strokeWidth="10"/>

                  {/* Grade 9 - Blue (30%) */}
                  <circle cx="60" cy="60" r="45" fill="none" stroke="#3b82f6" strokeWidth="10"
                    strokeDasharray="84.8 283" strokeDashoffset="0" className="transition-all duration-1000"/>

                  {/* Grade 10 - Green (25%) */}
                  <circle cx="60" cy="60" r="45" fill="none" stroke="#10b981" strokeWidth="10"
                    strokeDasharray="70.7 283" strokeDashoffset="-84.8" className="transition-all duration-1000"/>

                  {/* Grade 11 - Purple (25%) */}
                  <circle cx="60" cy="60" r="45" fill="none" stroke="#8b5cf6" strokeWidth="10"
                    strokeDasharray="70.7 283" strokeDashoffset="-155.5" className="transition-all duration-1000"/>

                  {/* Grade 12 - Amber (20%) */}
                  <circle cx="60" cy="60" r="45" fill="none" stroke="#f59e0b" strokeWidth="10"
                    strokeDasharray="56.5 283" strokeDashoffset="-226.2" className="transition-all duration-1000"/>
                </svg>

                {/* Center Text */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-base font-bold text-gray-900">{chartData.totalStudents}</p>
                    <p className="text-xs text-gray-600">
                      {activeTab === 'overview' ? 'Students' :
                       activeTab === 'staff' ? 'Staff' : 'Events'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Dynamic Legend */}
              <div className="space-y-1">
                {chartData.distribution.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-2.5 h-2.5 rounded-full"
                        style={{ backgroundColor: item.color }}
                      ></div>
                      <span className="text-gray-700 text-sm">{item.grade}</span>
                    </div>
                    <span className="font-semibold text-gray-900 text-sm">
                      {item.count} ({item.percentage}%)
                    </span>
                  </div>
                ))}
              </div>

              <p className="text-sm text-gray-600 mt-2 leading-relaxed">
                {activeTab === 'overview'
                  ? 'Shows the current distribution of students across all grade levels. Grade 9 has the highest enrollment, indicating strong new admissions.'
                  : activeTab === 'staff'
                  ? 'Displays staff composition across different roles. Teachers form the majority, ensuring optimal student-teacher ratios.'
                  : 'Breakdown of school events by category. Academic and cultural events dominate the calendar, promoting holistic development.'
                }
              </p>
            </div>

            {/* Chart 2: Dynamic Trends Line Chart */}
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-3 border border-white/50 shadow-lg shadow-emerald-200/30 hover:shadow-xl hover:shadow-emerald-300/40 transition-all">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-semibold text-gray-800">
                  {activeTab === 'overview' ? 'Enrollment Trends' :
                   activeTab === 'staff' ? 'Staff Hiring Trends' : 'Event Frequency'}
                </h3>
                <span className="text-sm text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full font-medium">
                  {chartData.enrollmentGrowth} Growth
                </span>
              </div>

              {/* Line Chart Visualization */}
              <div className="relative h-24 mb-3">
                <svg className="w-full h-24" viewBox="0 0 300 96">
                  {/* Grid Lines */}
                  <defs>
                    <pattern id="grid" width="50" height="19" patternUnits="userSpaceOnUse">
                      <path d="M 50 0 L 0 0 0 19" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
                    </pattern>
                  </defs>
                  <rect width="300" height="96" fill="url(#grid)" />

                  {/* Gradient Fill */}
                  <defs>
                    <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3"/>
                      <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.05"/>
                    </linearGradient>
                  </defs>

                  {/* Area Fill */}
                  <path d="M 20 64 L 70 56 L 120 52 L 170 44 L 220 36 L 270 28 L 270 96 L 20 96 Z"
                    fill="url(#areaGradient)" className="transition-all duration-1000"/>

                  {/* Line */}
                  <path d="M 20 64 L 70 56 L 120 52 L 170 44 L 220 36 L 270 28"
                    fill="none" stroke="#3b82f6" strokeWidth="2.5" strokeLinecap="round"
                    className="transition-all duration-1000"/>

                  {/* Data Points */}
                  <circle cx="20" cy="64" r="3" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="70" cy="56" r="3" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="120" cy="52" r="3" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="170" cy="44" r="3" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="220" cy="36" r="3" fill="#3b82f6" className="transition-all duration-1000"/>
                  <circle cx="270" cy="28" r="3" fill="#3b82f6" className="transition-all duration-1000"/>
                </svg>
              </div>

              {/* Month Labels */}
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Aug</span>
                <span>Sep</span>
                <span>Oct</span>
                <span>Nov</span>
                <span>Dec</span>
                <span>Jan</span>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div className="text-center">
                  <p className="text-base font-bold text-emerald-600">
                    +{chartData.monthlyEnrollments[chartData.monthlyEnrollments.length - 1]}
                  </p>
                  <p className="text-sm text-gray-600">This Month</p>
                </div>
                <div className="text-center">
                  <p className="text-base font-bold text-blue-600">
                    {chartData.monthlyEnrollments.reduce((a, b) => a + b, 0)}
                  </p>
                  <p className="text-sm text-gray-600">6-Month Total</p>
                </div>
              </div>

              <p className="text-sm text-gray-600 leading-relaxed">
                {activeTab === 'overview'
                  ? 'Monthly enrollment trends show consistent growth with peak admissions in January. The upward trajectory indicates strong school reputation and demand.'
                  : activeTab === 'staff'
                  ? 'Staff hiring patterns reflect strategic growth planning. Steady recruitment ensures optimal staffing levels and quality education delivery.'
                  : 'Event scheduling shows balanced distribution throughout the academic year. Peak activity periods align with academic calendar milestones.'
                }
              </p>
            </div>

            {/* Chart 3: Dynamic Performance Metrics Bar Chart */}
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-3 border border-white/50 shadow-lg shadow-purple-200/30 hover:shadow-xl hover:shadow-purple-300/40 transition-all">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-semibold text-gray-800">
                  {activeTab === 'overview' ? 'Key Metrics' :
                   activeTab === 'staff' ? 'Staff Performance' : 'Event Success'}
                </h3>
                <span className="text-sm text-purple-600 bg-purple-100 px-2 py-1 rounded-full font-medium">
                  {Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 90 ? 'Excellent' :
                   Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 80 ? 'Good' : 'Fair'}
                </span>
              </div>

              {/* Dynamic Bar Chart Visualization */}
              <div className="space-y-3 mb-3">
                {/* Attendance Rate */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {activeTab === 'overview' ? 'Attendance Rate' :
                       activeTab === 'staff' ? 'Staff Attendance' : 'Event Attendance'}
                    </span>
                    <span className="text-sm font-bold text-emerald-600">{chartData.metrics.attendance}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-gradient-to-r from-emerald-400 to-emerald-500 h-1.5 rounded-full transition-all duration-1000"
                      style={{width: `${chartData.metrics.attendance}%`}}></div>
                  </div>
                </div>

                {/* Academic Performance */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {activeTab === 'overview' ? 'Academic Performance' :
                       activeTab === 'staff' ? 'Performance Rating' : 'Educational Value'}
                    </span>
                    <span className="text-sm font-bold text-blue-600">{chartData.metrics.academic}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-gradient-to-r from-blue-400 to-blue-500 h-1.5 rounded-full transition-all duration-1000"
                      style={{width: `${chartData.metrics.academic}%`}}></div>
                  </div>
                </div>

                {/* Teacher Satisfaction */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {activeTab === 'overview' ? 'Teacher Satisfaction' :
                       activeTab === 'staff' ? 'Job Satisfaction' : 'Organizer Rating'}
                    </span>
                    <span className="text-sm font-bold text-purple-600">{chartData.metrics.teacherSat}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-gradient-to-r from-purple-400 to-purple-500 h-1.5 rounded-full transition-all duration-1000"
                      style={{width: `${chartData.metrics.teacherSat}%`}}></div>
                  </div>
                </div>

                {/* Parent Engagement */}
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {activeTab === 'overview' ? 'Parent Engagement' :
                       activeTab === 'staff' ? 'Community Relations' : 'Parent Participation'}
                    </span>
                    <span className="text-sm font-bold text-amber-600">{chartData.metrics.parentEng}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-gradient-to-r from-amber-400 to-amber-500 h-1.5 rounded-full transition-all duration-1000"
                      style={{width: `${chartData.metrics.parentEng}%`}}></div>
                  </div>
                </div>
              </div>

              {/* Dynamic Summary Stats */}
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div className="text-center p-1.5 bg-emerald-50 rounded-lg">
                  <p className="text-sm font-bold text-emerald-600">
                    {Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 90 ? 'A+' :
                     Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 80 ? 'A' : 'B+'}
                  </p>
                  <p className="text-sm text-gray-600">Overall Grade</p>
                </div>
                <div className="text-center p-1.5 bg-blue-50 rounded-lg">
                  <p className="text-sm font-bold text-blue-600">
                    {activeTab === 'overview' ? 'Top 5%' :
                     activeTab === 'staff' ? 'Excellent' : 'High Impact'}
                  </p>
                  <p className="text-sm text-gray-600">
                    {activeTab === 'overview' ? 'District Rank' :
                     activeTab === 'staff' ? 'Rating' : 'Success Rate'}
                  </p>
                </div>
              </div>

              <p className="text-sm text-gray-600 leading-relaxed">
                {activeTab === 'overview'
                  ? 'Comprehensive performance indicators across key areas. High attendance and academic scores reflect excellent school management and student engagement.'
                  : activeTab === 'staff'
                  ? 'Staff performance metrics demonstrate high professional standards. Strong satisfaction scores indicate positive work environment and effective leadership.'
                  : 'Event success metrics show excellent planning and execution. High participation rates and positive feedback reflect strong community engagement.'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Inspirational Quote - Compact Design */}
      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-700 rounded-xl p-3 shadow-xl shadow-indigo-300/30 border border-indigo-200/20 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
        <div className="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -translate-y-10 translate-x-10"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full translate-y-8 -translate-x-8"></div>

        {/* Content */}
        <div className="relative z-10">
          <div className="flex items-start space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
              </svg>
            </div>
            <div className="flex-1">
              <blockquote className="text-white/95 text-sm font-medium leading-relaxed mb-2">
                "Education is the most powerful weapon which you can use to change the world. Every school is a beacon of hope, shaping minds that will build a better tomorrow for our nation."
              </blockquote>

              {/* Author - Right Aligned */}
              <div className="flex items-center justify-between">
                <p className="text-white/80 text-sm flex items-center">
                  🌟 Building tomorrow's leaders, one student at a time
                </p>
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">NM</span>
                  </div>
                  <div className="text-right">
                    <p className="text-white/90 text-sm font-semibold">Nelson Mandela</p>
                    <p className="text-white/70 text-sm">Former President of South Africa</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default DashboardContent;
