@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/*
===============================================================================
                        EDUPRO DESIGN SYSTEM - GLOBALS.CSS
===============================================================================

This file contains the centralized design system for the EduPro application.
All components should use these standardized classes for consistency.

DESIGN SYSTEM OVERVIEW:
- Typography: Consistent font sizes with responsive scaling
- Buttons: Standardized sizes (sm, md, lg) with consistent styling
- Forms: Unified input and select components with proper sizing
- Tables: Consistent header and cell styling with proper spacing
- Cards: Standardized padding and typography hierarchy
- Modals: Consistent sizing and styling across the application

USAGE GUIDELINES:
1. Always use the standardized classes (e.g., btn-primary, form-input-md)
2. Avoid inline styles or custom CSS unless absolutely necessary
3. Use the responsive typography system (text-xs-app, text-sm-app, etc.)
4. Follow the established component hierarchy (card-sm, card-md, card-lg)

COMPONENT SIZES:
- Small (sm): Compact components for dense layouts
- Medium (md): Default size for most components
- Large (lg): Prominent components and headers

===============================================================================
*/

/* Global Typography - Professional Sans Font System */
@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  html {
    scroll-behavior: smooth;
    font-size: 14px; /* Set explicit base font size for rem calculations */
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-weight: 400;
    line-height: 1.4; /* Tighter line height for compactness */
    color: #1e293b; /* slate-800 */
    background-color: #f8fafc; /* slate-50 */
    font-size: 1rem; /* Use standard 1rem base size for scalability */
  }
}

/* Professional Typography Scale - Rem-based System */
@layer components {
  /* Base typography classes with consistent rem scaling */
  .text-xs-app {
    font-size: 0.75rem; /* 12px - Extra small text */
    line-height: 1rem; /* 16px - Tight line height */
    font-weight: 400;
  }

  .text-sm-app {
    font-size: 0.875rem; /* 14px - Small text */
    line-height: 1.25rem; /* 20px - Comfortable line height */
    font-weight: 400;
  }

  .text-base-app {
    font-size: 1rem; /* 16px - Base text size */
    line-height: 1.5rem; /* 24px - Standard line height */
    font-weight: 400;
  }

  .text-lg-app {
    font-size: 1.125rem; /* 18px - Large text */
    line-height: 1.75rem; /* 28px - Proportional line height */
    font-weight: 500;
  }

  .text-xl-app {
    font-size: 1.25rem; /* 20px - Extra large text */
    line-height: 1.875rem; /* 30px - Proportional line height */
    font-weight: 600;
  }

  .text-2xl-app {
    font-size: 1.5rem; /* 24px - 2X large text */
    line-height: 2rem; /* 32px - Proportional line height */
    font-weight: 700;
  }

  .text-3xl-app {
    font-size: 1.875rem; /* 30px - 3X large text */
    line-height: 2.25rem; /* 36px - Proportional line height */
    font-weight: 700;
  }

  .text-4xl-app {
    font-size: 2.25rem; /* 36px - 4X large text */
    line-height: 2.5rem; /* 40px - Proportional line height */
    font-weight: 800;
  }

  .text-5xl-app {
    font-size: 3rem; /* 48px - 5X large text */
    line-height: 3rem; /* 48px - Tight line height for display */
    font-weight: 800;
  }
  /* Responsive typography classes - Rem-based scaling */
  .text-responsive-sm {
    font-size: 0.875rem; /* 14px - Small on mobile */
    line-height: 1.25rem; /* 20px */
  }

  @media (min-width: 768px) {
    .text-responsive-sm {
      font-size: 1rem; /* 16px - Base on desktop */
      line-height: 1.5rem; /* 24px */
    }
  }

  .text-responsive-base {
    font-size: 1rem; /* 16px - Base on mobile */
    line-height: 1.5rem; /* 24px */
  }

  @media (min-width: 768px) {
    .text-responsive-base {
      font-size: 1.125rem; /* 18px - Larger on desktop */
      line-height: 1.75rem; /* 28px */
    }
  }

  .text-responsive-lg {
    font-size: 1.125rem; /* 18px - Large on mobile */
    line-height: 1.75rem; /* 28px */
  }

  @media (min-width: 768px) {
    .text-responsive-lg {
      font-size: 1.25rem; /* 20px - Extra large on desktop */
      line-height: 1.875rem; /* 30px */
    }
  }

  .text-responsive-xl {
    font-size: 1.25rem; /* 20px - Extra large on mobile */
    line-height: 1.875rem; /* 30px */
  }

  @media (min-width: 768px) {
    .text-responsive-xl {
      font-size: 1.5rem; /* 24px - 2X large on desktop */
      line-height: 2rem; /* 32px */
    }
  }

  .text-responsive-2xl {
    font-size: 1.5rem; /* 24px - 2X large on mobile */
    line-height: 2rem; /* 32px */
  }

  @media (min-width: 768px) {
    .text-responsive-2xl {
      font-size: 1.875rem; /* 30px - 3X large on desktop */
      line-height: 2.25rem; /* 36px */
    }
  }

  .text-responsive-3xl {
    font-size: 1.875rem; /* 30px - 3X large on mobile */
    line-height: 2.25rem; /* 36px */
  }

  @media (min-width: 768px) {
    .text-responsive-3xl {
      font-size: 2.25rem; /* 36px - 4X large on desktop */
      line-height: 2.5rem; /* 40px */
    }
  }

  .text-responsive-4xl {
    font-size: 2.25rem; /* 36px - 4X large on mobile */
    line-height: 2.5rem; /* 40px */
  }

  @media (min-width: 768px) {
    .text-responsive-4xl {
      font-size: 3rem; /* 48px - 5X large on desktop */
      line-height: 3rem; /* 48px */
    }
  }

  .text-responsive-5xl {
    font-size: 2.5rem; /* 40px - 5X large on mobile */
    line-height: 2.75rem; /* 44px */
  }

  @media (min-width: 768px) {
    .text-responsive-5xl {
      font-size: 3.75rem; /* 60px - 6X large on desktop */
      line-height: 3.75rem; /* 60px */
    }
  }
  
  /* Legacy Form Components - Updated to use standardized system */
  .form-input {
    @apply form-input-md;
  }

  .form-input:focus {
    @apply ring-2 ring-indigo-500/50 border-indigo-500/50 shadow-lg shadow-indigo-500/10;
  }

  .form-input.error {
    @apply border-red-300 focus:ring-red-500/50 focus:border-red-500/50 bg-red-50/50;
  }

  .form-input.success {
    @apply border-green-300 focus:ring-green-500/50 focus:border-green-500/50 bg-green-50/50;
  }

  .form-label {
    @apply text-sm-app font-medium text-slate-700 block;
    margin-bottom: 0.375rem; /* Convert mb-1.5 to rem */
  }

  .form-label.required::after {
    content: " *";
    @apply text-red-500;
  }

  .form-error {
    @apply text-xs-app text-red-600 mt-1 flex items-center;
  }

  .form-success {
    @apply text-xs-app text-green-600 mt-1 flex items-center;
  }

  /* Responsive form inputs for mobile */
  @media (max-width: 640px) {
    .form-input {
      @apply form-input-sm;
    }
  }

  .form-button {
    @apply btn-md;
  }

  .form-button-primary {
    @apply btn-primary;
  }

  .form-button-secondary {
    @apply btn-secondary;
  }

  /* Legacy Card components - Updated to use standardized system */
  .card {
    @apply card-md;
  }

  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600;
  }

  /* Legacy Table components - Updated to use standardized system */
  .table-header {
    @apply table-header-cell;
  }

  .table-cell {
    @apply text-base-app text-slate-700;
  }
  
  /* ===== STANDARDIZED DESIGN SYSTEM ===== */

  /* Button System - Consistent sizing and typography */
  .btn {
    @apply text-base-app font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    font-family: inherit;
    /* Standardized button sizes */
  }

  .btn-sm {
    @apply btn text-sm-app;
    padding: 0.5rem 0.75rem; /* Convert px-3 py-2 to rem */
    height: 2.25rem; /* Keep rem for consistent scaling */
  }

  .btn-md {
    @apply btn text-base-app;
    padding: 0.625rem 1rem; /* Convert px-4 py-2.5 to rem */
    height: 2.5rem; /* Keep rem for consistent scaling */
  }

  .btn-lg {
    @apply btn text-base-app;
    padding: 0.75rem 1.25rem; /* Convert px-5 py-3 to rem */
    height: 2.75rem; /* Keep rem for consistent scaling */
  }

  .btn-primary {
    @apply btn-md bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply btn-md bg-slate-200 text-slate-700 hover:bg-slate-300 focus:ring-slate-500 border border-slate-300;
  }

  .btn-danger {
    @apply btn-md bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply btn-md bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-sm hover:shadow-md;
  }

  .btn-outline {
    @apply btn-md bg-white text-slate-700 hover:bg-slate-50 focus:ring-slate-500 border border-slate-300 shadow-sm hover:shadow-md;
  }

  /* Form System - Consistent input sizing and typography */
  .form-input-sm {
    @apply text-sm-app border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    padding: 0.5rem 0.75rem; /* Convert px-3 py-2 to rem */
    height: 2.25rem; /* Keep rem for consistent scaling */
    font-family: inherit;
  }

  .form-input-md {
    @apply text-base-app border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    padding: 0.625rem 1rem; /* Convert px-4 py-2.5 to rem */
    height: 2.5rem; /* Keep rem for consistent scaling */
    font-family: inherit;
  }

  .form-input-lg {
    @apply text-base-app border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    padding: 0.75rem 1rem; /* Convert px-4 py-3 to rem */
    height: 2.75rem; /* Keep rem for consistent scaling */
    font-family: inherit;
  }

  /* Default form input - medium size */
  .form-input {
    @apply form-input-md;
  }

  /* Select/Dropdown System */
  .form-select-sm {
    @apply form-input-sm appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem; /* Keep rem for consistent scaling */
  }

  .form-select-md {
    @apply form-input-md appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem; /* Keep rem for consistent scaling */
  }

  .form-select-lg {
    @apply form-input-lg appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem; /* Keep rem for consistent scaling */
  }

  /* Default form select - medium size */
  .form-select {
    @apply form-select-md;
  }

  /* Table System - Consistent typography and spacing */
  .table-container {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden;
  }

  .table-header {
    @apply bg-gray-800 text-gray-200;
  }

  .table-header-cell {
    @apply text-left text-sm-app font-medium uppercase tracking-wide;
    padding: 0.75rem 1.5rem; /* Convert px-6 py-3 to rem */
    height: 3rem; /* Keep rem for consistent scaling */
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150 border-b border-gray-200 last:border-b-0;
  }

  .table-cell {
    @apply text-base-app text-gray-900;
    padding: 0.75rem 1.5rem; /* Convert px-6 py-3 to rem */
    height: 3.5rem; /* Keep rem for consistent scaling */
  }

  .table-cell-secondary {
    @apply text-sm-app text-gray-600;
    padding: 0.75rem 1.5rem; /* Convert px-6 py-3 to rem */
    height: 3.5rem; /* Keep rem for consistent scaling */
  }

  /* Card System - Consistent padding and typography */
  .card-sm {
    @apply bg-white rounded-lg shadow-sm border border-slate-200;
    padding: 1rem; /* Convert p-4 to rem */
  }

  .card-md {
    @apply bg-white rounded-lg shadow-sm border border-slate-200;
    padding: 1.5rem; /* Convert p-6 to rem */
  }

  .card-lg {
    @apply bg-white rounded-xl shadow-lg border border-slate-200;
    padding: 2rem; /* Convert p-8 to rem */
  }

  /* Default card - medium size */
  .card {
    @apply card-md;
  }

  .card-title-sm {
    @apply text-base-app font-semibold text-slate-900;
    margin-bottom: 0.5rem; /* Convert mb-2 to rem */
  }

  .card-title-md {
    @apply text-lg-app font-semibold text-slate-900;
    margin-bottom: 0.75rem; /* Convert mb-3 to rem */
  }

  .card-title-lg {
    @apply text-xl-app font-bold text-slate-900;
    margin-bottom: 1rem; /* Convert mb-4 to rem */
  }

  /* Default card title - medium size */
  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700 leading-relaxed;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600;
    margin-bottom: 0.5rem; /* Convert mb-2 to rem */
  }

  /* Section Header System - Consistent hierarchy */
  .section-header-sm {
    @apply text-lg-app font-semibold text-gray-900 mb-3;
  }

  .section-header-md {
    @apply text-xl-app font-bold text-gray-900 mb-4;
  }

  .section-header-lg {
    @apply text-2xl-app font-bold text-gray-900 mb-6;
  }

  .section-header-xl {
    @apply text-3xl-app font-bold text-gray-900 mb-8;
  }

  /* Page Header System */
  .page-header {
    @apply section-header-lg;
  }

  .page-subtitle {
    @apply text-base-app text-gray-600 mt-1;
  }

  /* Modal System - Consistent sizing and typography */
  .modal-overlay {
    @apply fixed inset-0 bg-gradient-to-br from-slate-900/90 via-slate-800/90 to-slate-900/90 flex items-center justify-center backdrop-blur-md;
    padding: 1rem; /* Convert p-4 to rem */
    z-index: 9999; /* Ensure modal is above all other elements */
    min-height: 100vh; /* Ensure full viewport coverage */
    min-height: 100dvh; /* Use dynamic viewport height for mobile */
  }

  .modal-container-sm {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-sm mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-md {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-md mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-lg {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-lg mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-xl {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-xl mx-auto relative shadow-2xl border border-white/20;
  }

  /* Default modal - medium size */
  .modal-container {
    @apply modal-container-md;
  }

  .modal-header {
    @apply border-b border-gray-200;
    padding: 1rem 1.5rem; /* Convert px-6 py-4 to rem */
  }

  .modal-title {
    @apply text-lg-app font-semibold text-gray-900;
  }

  .modal-content {
    @apply text-base-app text-gray-700;
    padding: 1rem 1.5rem; /* Convert px-6 py-4 to rem */
  }

  .modal-footer {
    @apply border-t border-gray-200 flex items-center justify-end;
    padding: 1rem 1.5rem; /* Convert px-6 py-4 to rem */
    gap: 0.75rem; /* Convert space-x-3 to gap in rem */
  }
  
  /* Modal and overlay components */
  .modal-title {
    @apply text-xl-app font-semibold text-slate-900;
  }
  
  .modal-content {
    @apply text-base-app text-slate-700;
  }
}

/* Custom fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1.875rem); /* Convert 30px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-3.125rem); /* Convert -50px to rem */
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(3.125rem); /* Convert 50px to rem */
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Modern Auth Modal Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-1.25rem) scale(0.95); /* Convert -20px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced dropdown slideIn animation */
@keyframes slideInDropdown {
  from {
    opacity: 0;
    transform: translateY(-0.625rem) scale(0.98); /* Convert -10px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalBackdrop {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(0.75rem); /* Convert 12px to rem */
  }
}

@keyframes shimmer {
  0% {
    background-position: -12.5rem 0; /* Convert -200px to rem */
  }
  100% {
    background-position: calc(12.5rem + 100%) 0; /* Convert 200px to rem */
  }
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(99 102 241 / 0.3);
    box-shadow: 0 0 0 0 rgb(99 102 241 / 0.7);
  }
  70% {
    border-color: rgb(99 102 241 / 0.6);
    box-shadow: 0 0 0 0.1875rem rgb(99 102 241 / 0); /* Convert 3px to rem */
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-float {
  animation: floatAnimation 3s ease-in-out infinite;
}

.animate-pulseGlow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-modalBackdrop {
  animation: modalBackdrop 0.3s ease-out;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 12.5rem 100%; /* Convert 200px to rem */
  animation: shimmer 2s infinite;
}

.animate-pulse-border {
  animation: pulse-border 2s infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-0.5rem); /* Convert -8px to rem */
  box-shadow: 0 1.25rem 2.5rem rgba(0, 0, 0, 0.15); /* Convert 20px 40px to rem */
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(0.625rem); /* Convert 10px to rem */
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg transition-all duration-200 hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl transform;
  padding: 0.75rem 1.5rem; /* Convert py-3 px-6 to rem */
}

.btn-primary:hover {
  transform: translateY(-0.25rem); /* Convert hover:-translate-y-1 to rem */
}

.btn-secondary {
  @apply border-2 border-indigo-600 text-indigo-600 font-semibold rounded-lg transition-all duration-200 hover:bg-indigo-600 hover:text-white transform;
  padding: 0.75rem 1.5rem; /* Convert py-3 px-6 to rem */
}

.btn-secondary:hover {
  transform: translateY(-0.25rem); /* Convert hover:-translate-y-1 to rem */
}

/* Loading state for form submissions */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 1.25rem; /* Convert 20px to rem */
  height: 1.25rem; /* Convert 20px to rem */
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced focus states for accessibility */
.focus-visible:focus {
  outline: 2px solid #6366f1;
  outline-offset: 0.125rem; /* Convert 2px to rem */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 0.5rem; /* Convert 8px to rem */
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 0.25rem; /* Convert 4px to rem */
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
  background-size: 1.25rem 1.25rem; /* Convert 20px to rem */
}

/* Enhanced form field styles */
.form-field-enhanced {
  @apply relative;
}

.form-field-enhanced input:focus + .field-icon {
  @apply text-indigo-500 scale-110;
}

.form-field-enhanced:hover .field-icon {
  @apply text-slate-500;
}

/* Loading spinner enhancement */
@keyframes spin-enhanced {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner-enhanced {
  animation: spin-enhanced 1s linear infinite;
}

/* Enhanced sidebar and navigation styles */

/* Sidebar navigation hover effects */
.nav-item-enhanced {
  @apply relative overflow-hidden;
}

.nav-item-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-item-enhanced:hover::before {
  left: 100%;
}

/* Active nav item glow effect */
.nav-item-active {
  box-shadow:
    0 0 1.25rem rgba(99, 102, 241, 0.3), /* Convert 20px to rem */
    0 0 2.5rem rgba(99, 102, 241, 0.1), /* Convert 40px to rem */
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}  /* Amazing Professional Sidebar - Blue Glass Theme with Purple Accents */
  .sidebar-dark {
    background: linear-gradient(180deg,
      rgba(59, 130, 246, 0.08) 0%,     /* Very light blue */
      rgba(147, 197, 253, 0.12) 25%,   /* Light blue */
      rgba(219, 234, 254, 0.10) 50%,   /* Soft blue */
      rgba(147, 197, 253, 0.12) 75%,   /* Light blue */
      rgba(59, 130, 246, 0.08) 100%    /* Very light blue */
    );
    /* Glass morphism effect overlay */
    position: relative;
    box-shadow:
      0.75rem 0 3rem -0.25rem rgba(59, 130, 246, 0.15),
      0 0.5rem 2rem -0.125rem rgba(59, 130, 246, 0.08),
      inset -1px 0 0 rgba(255, 255, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.6),
      inset 0 -1px 0 rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(20px) saturate(1.2);
    border-right: 1px solid rgba(59, 130, 246, 0.2);
  }

  /* Glass effect overlay for popping out appearance */
  .sidebar-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.3) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(59, 130, 246, 0.05) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  .sidebar-dark .sidebar-header {
    @apply border-blue-200/30;
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.12) 0%,     /* Light blue */
      rgba(147, 197, 253, 0.15) 30%,   /* Medium light blue */
      rgba(219, 234, 254, 0.18) 50%,   /* Soft blue center */
      rgba(147, 197, 253, 0.15) 70%,   /* Medium light blue */
      rgba(59, 130, 246, 0.12) 100%    /* Light blue */
    );
    backdrop-filter: blur(24px) saturate(1.3);
    box-shadow:
      0 3px 12px rgba(59, 130, 246, 0.12),
      0 1px 6px rgba(59, 130, 246, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(59, 130, 246, 0.15);
    position: relative;
    z-index: 2;
  }

  .sidebar-dark .sidebar-brand {
    @apply font-bold;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 40%, #60a5fa 80%, #93c5fd 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.15));
  }  .sidebar-dark .sidebar-toggle-btn {
    @apply text-slate-600 hover:text-slate-800 transition-all duration-300;
    @apply hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/80;
    @apply hover:shadow-lg hover:shadow-blue-400/20;
    border-radius: 0.5rem;
  }

  .sidebar-dark .sidebar-nav-item {
    @apply text-slate-800 transition-all duration-300 relative overflow-hidden;
    @apply hover:text-slate-900;
    margin: 0 0.75rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(16px) saturate(1.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.4) 0%,     /* Glass white */
      rgba(255, 255, 255, 0.2) 50%,    /* Transparent center */
      rgba(255, 255, 255, 0.4) 100%    /* Glass white */
    );
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.6),
      inset 0 -1px 0 rgba(59, 130, 246, 0.05);
    position: relative;
    z-index: 2;
  }

  .sidebar-dark .sidebar-nav-item:hover {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.7) 0%,
      rgba(255, 255, 255, 0.5) 50%,
      rgba(255, 255, 255, 0.7) 100%
    );
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.12),
      0 3px 10px rgba(59, 130, 246, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
  }  .sidebar-dark .sidebar-nav-item::before {
    content: '';
    @apply absolute left-0 top-0 bottom-0 w-1 transition-all duration-300;
    background: linear-gradient(180deg, transparent 0%, #3b82f6 50%, transparent 100%);
    opacity: 0;
    transform: scaleY(0);
    border-radius: 0 0.25rem 0.25rem 0;
  }

  .sidebar-dark .sidebar-nav-item:hover::before {
    opacity: 1;
    transform: scaleY(1);
  }

  /* Amazing Active item styling with sophisticated purple contrast */
  .sidebar-dark .sidebar-nav-item.active {
    @apply text-slate-900 relative;
    background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.15) 0%,     /* Light purple */
      rgba(196, 181, 253, 0.18) 25%,   /* Medium light purple */
      rgba(233, 213, 255, 0.15) 50%,   /* Soft purple center */
      rgba(196, 181, 253, 0.18) 75%,   /* Medium light purple */
      rgba(139, 92, 246, 0.15) 100%    /* Light purple */
    );
    box-shadow:
      0 8px 25px rgba(139, 92, 246, 0.18),
      0 4px 12px rgba(139, 92, 246, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(139, 92, 246, 0.1);
    border-left: 3px solid rgba(139, 92, 246, 0.9);
    border-color: rgba(139, 92, 246, 0.4);
    backdrop-filter: blur(20px) saturate(1.3);
    position: relative;
    z-index: 3;
  }

  /* Active item glow effect */
  .sidebar-dark .sidebar-nav-item.active::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.1) 0%,
      rgba(196, 181, 253, 0.08) 50%,
      rgba(139, 92, 246, 0.1) 100%
    );
    border-radius: 0.875rem;
    z-index: -1;
    filter: blur(4px);
  }
  /* Arrow removed for cleaner look */
  .sidebar-dark .sidebar-nav-item .sidebar-label {
    @apply text-slate-700 font-medium;
  }

  .sidebar-dark .sidebar-nav-item.active .sidebar-label {
    @apply text-slate-900 font-semibold;
    text-shadow: 0 1px 2px rgba(59, 130, 246, 0.15);
  }

  .sidebar-dark .sidebar-subitem {
    @apply text-slate-600 transition-all duration-300 relative;
    @apply hover:text-slate-800;
    margin-left: 0.75rem;
    @apply border-l border-slate-300/50;
    border-radius: 0.5rem;
    background: linear-gradient(90deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(241, 245, 249, 0.5) 100%
    );
  }

  .sidebar-dark .sidebar-subitem:hover {
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(241, 245, 249, 0.9) 100%
    );
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.08);
    border-left-color: rgba(59, 130, 246, 0.4);
  }

  .sidebar-dark .sidebar-subitem::before {
    content: '';
    @apply absolute -left-px top-1/2 -translate-y-1/2 w-3 h-px bg-slate-400/60;
  }
  
  .sidebar-dark .sidebar-subitem.active {
    @apply text-blue-700 font-medium;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.1) 100%);
    border-left-color: rgba(59, 130, 246, 0.6);
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
  }

  .sidebar-dark .sidebar-subitem.active::before {
    @apply bg-blue-500;
    opacity: 0.9;
  }

  .sidebar-dark .sidebar-profile {
    @apply text-slate-800 transition-all duration-300;
    @apply hover:text-slate-900;
    margin: 0 0.75rem;
    border-radius: 0.875rem;
    backdrop-filter: blur(18px) saturate(1.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 0.3) 30%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.3) 70%,
      rgba(255, 255, 255, 0.5) 100%
    );
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.7),
      inset 0 -1px 0 rgba(59, 130, 246, 0.05);
    position: relative;
    z-index: 2;
  }

  .sidebar-dark .sidebar-profile:hover {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(255, 255, 255, 0.8) 100%
    );
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.15),
      0 4px 12px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
  }

  .sidebar-dark .sidebar-tooltip {
    @apply text-slate-800 shadow-xl;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(248, 250, 252, 0.95) 100%
    );
    box-shadow:
      0 12px 30px rgba(59, 130, 246, 0.15),
      0 6px 16px rgba(59, 130, 246, 0.1),
      0 0 0 1px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(24px) saturate(1.2);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 9999;
    pointer-events: none;
    transform: translateX(0.5rem);
  }

  .sidebar-dark .sidebar-tooltip::before {
    @apply border-r-gray-200;
    border-right-color: rgba(248, 250, 252, 0.98);
  }  /* Modern Collapse Toggle Arrow - Professional Light Theme */
  .sidebar-collapse-toggle {
    @apply absolute top-1/2 z-50;
    @apply bg-gradient-to-r from-blue-500 to-blue-600 rounded-full;
    @apply flex items-center justify-center cursor-pointer transition-all duration-300;
    @apply hover:from-blue-600 hover:to-blue-700 hover:scale-110;
    @apply shadow-lg shadow-blue-500/30;
    right: -1.25rem;
    transform: translateY(-50%);
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid rgba(255, 255, 255, 0.9);
  }

  .sidebar-collapse-toggle:hover {
    box-shadow: 0 0 1.5625rem rgba(59, 130, 246, 0.5);
  }

  .sidebar-collapse-toggle svg {
    @apply w-5 h-5 text-white transition-transform duration-300; /* Increased icon size */
  }

  .sidebar-collapse-toggle svg {
    @apply w-4 h-4 text-white transition-transform duration-300;
  }

  .sidebar-collapse-toggle.collapsed svg {
    @apply rotate-180;
  }
  /* Professional Header to Match Light Sidebar */
  .header-dark {
    @apply bg-gradient-to-r from-slate-50 to-slate-100/95 border-slate-300/60 shadow-slate-400/10;
    background-color: rgb(248 250 252);
    box-shadow: 0 1px 3px rgba(15, 23, 42, 0.08), 0 1px 2px rgba(15, 23, 42, 0.06);
    backdrop-filter: blur(15px);
  }
  
  .header-dark .header-title {
    @apply text-slate-800;
    font-size: 1.2375rem;
    font-weight: 700;
  }

  .header-dark .header-breadcrumb {
    @apply text-slate-500;
  }

  .header-dark .header-breadcrumb-current {
    @apply text-slate-700;
  }

  .header-dark .header-search-input {
    @apply bg-white border-slate-300 text-slate-800 placeholder-slate-500;
    @apply focus:bg-white focus:border-slate-400 focus:ring-2 focus:ring-blue-500/20;
  }

  .header-dark .header-action-btn {
    @apply text-slate-600 hover:text-slate-800 hover:bg-slate-100;
  }

  .header-dark .header-profile-dropdown {
    @apply bg-white border-slate-300 shadow-slate-400/20;
  }

  .header-dark .header-profile-name {
    @apply text-slate-800;
  }

  .header-dark .header-profile-role {
    @apply text-slate-500;
  }

  .header-dark .header-dropdown-item {
    @apply text-slate-700 hover:bg-slate-100 hover:text-slate-900;
  }

  .header-dark .header-dropdown-item.danger {
    @apply text-red-600 hover:bg-red-50 hover:text-red-700;
  }

  /* Consistent header height */
  .header-height {
    @apply h-16; /* 64px height */
  }

  .sidebar-header-height {
    @apply h-16; /* 64px height to match header */
  }

  /* Enhanced glass morphism effect */
  .glass-morphism-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Professional shadow utilities */
  .shadow-professional {
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .shadow-professional-lg {
    box-shadow: 
      0 10px 25px rgba(0, 0, 0, 0.1),
      0 6px 10px rgba(0, 0, 0, 0.12);
  }
  /* Enhanced animation keyframes */
  @keyframes tooltipSlideIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) translateX(4px);
    }
  }

  @keyframes slideIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }
  }

  /* System status indicator animation */
  @keyframes pulse-gentle {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  .pulse-gentle {
    animation: pulse-gentle 2s infinite ease-in-out;
  }

  /* Enhanced gradient text */
  .gradient-text-professional {
    background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #334155 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Custom sidebar font sizes - 5% smaller */
  .text-xs-small {
    font-size: 0.7125rem; /* 0.75rem * 0.95 = 0.7125rem */
    line-height: 1.1875rem; /* 1.25rem * 0.95 = 1.1875rem */
  }

  .text-sm-small {
    font-size: 0.83125rem; /* 0.875rem * 0.95 = 0.83125rem */
    line-height: 1.30625rem; /* 1.375rem * 0.95 = 1.30625rem */
  }

  /* Custom sidebar font sizes - 5% larger than original */
  .text-xs-large {
    font-size: 0.7875rem; /* 0.75rem * 1.05 = 0.7875rem */
    line-height: 1.3125rem; /* 1.25rem * 1.05 = 1.3125rem */
  }

  .text-sm-large {
    font-size: 0.91875rem; /* 0.875rem * 1.05 = 0.91875rem */
    line-height: 1.44375rem; /* 1.375rem * 1.05 = 1.44375rem */
  }

  /* Custom sidebar widths - 3% wider */
  .w-14-plus {
    width: 3.625rem; /* 56px * 1.03 = 57.68px ≈ 58px = 3.625rem */
  }

  .w-62 {
    width: 15.5rem; /* 240px * 1.03 = 247.2px ≈ 248px = 15.5rem */
  }

  /* Custom margins to match sidebar widths */
  .ml-14-plus {
    margin-left: 3.625rem; /* Match w-14-plus */
  }

  .ml-62 {
    margin-left: 15.5rem; /* Match w-62 */
  }

  /* Custom left positioning for separator */
  .left-14-plus {
    left: 3.625rem; /* Match w-14-plus */
  }

  .left-62 {
    left: 15.5rem; /* Match w-62 */
  }

  /* Smooth Animation for Tooltips */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-10px) translateY(-50%);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateY(-50%);
    }
  }

  /* Collapsed Sidebar Styles */
  @layer components {    /* Enhanced icons for collapsed sidebar with perfect centering */
    .sidebar-icon-collapsed {
      @apply w-6 h-6; /* Increased size for better visibility while staying within bounds */
      flex-shrink: 0; /* Prevent icon from shrinking */
      display: block;
      margin: 0 auto; /* Ensure perfect horizontal centering */
    }

    .sidebar-icon-expanded {
      @apply w-5 h-5; /* Keep current size for expanded state */
      flex-shrink: 0; /* Prevent icon from shrinking */
    }    /* Enhanced Tooltip styles for collapsed sidebar - Professional Theme */
    .sidebar-tooltip {
      @apply absolute left-full ml-4 px-3 py-2 text-white text-sm-app rounded-lg shadow-xl;
      @apply opacity-0 invisible transform -translate-y-1/2 transition-all duration-300 ease-out;
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
      border: 1px solid rgba(59, 130, 246, 0.3);
      box-shadow: 0 10px 25px rgba(15, 23, 42, 0.4), 0 0 20px rgba(59, 130, 246, 0.2);
      white-space: nowrap;
      top: 50%;
      z-index: 9999;
      pointer-events: none;
      backdrop-filter: blur(10px);
    }

    .sidebar-tooltip::before {
      content: '';
      @apply absolute right-full top-1/2 -translate-y-1/2;
      border: 6px solid transparent;
      border-right: 6px solid #374151;
      filter: drop-shadow(-1px 0 1px rgba(59, 130, 246, 0.1));
    }

    .sidebar-tooltip.show {
      @apply opacity-100 visible;
      transform: translateY(-50%) translateX(4px);
      animation: tooltipSlideIn 0.3s ease-out forwards;
    }    /* Enhanced hover states for collapsed sidebar with professional light theme */
    .sidebar-item-collapsed {
      @apply relative flex items-center justify-center rounded-xl transition-all duration-300;
      @apply text-slate-600 hover:text-slate-800;
      @apply mx-auto;
      width: 3rem;
      height: 3rem;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(148, 163, 184, 0.3);
      backdrop-filter: blur(8px);
    }
    
    .sidebar-item-collapsed:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.12) 100%);
      border-color: rgba(59, 130, 246, 0.4);
      transform: scale(1.05);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
    }

    /* Professional collapsed sidebar active icon styling */
    .sidebar-item-collapsed.active {
      @apply text-slate-800;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(147, 197, 253, 0.15) 100%);
      border-color: rgba(59, 130, 246, 0.5);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.25);
    }

    .sidebar-item-collapsed:hover .sidebar-tooltip {
      @apply opacity-100 visible;
      transform: translateY(-50%) translateX(4px);
      animation: tooltipSlideIn 0.3s ease-out forwards;
    }

    /* Additional collapsed sidebar improvements */
  .sidebar-dark nav {
    overflow: hidden; /* Prevent any overflow issues */
  }

  /* Ensure proper spacing for collapsed navigation items */
  .sidebar-dark.w-16 nav {
    padding-left: 0.5rem; /* 8px padding for collapsed state */
    padding-right: 0.5rem; /* 8px padding for collapsed state */
  }

  /* Center all collapsed items perfectly within the 64px sidebar */
  .sidebar-dark .sidebar-item-collapsed {
    margin-left: auto;
    margin-right: auto;
    /* Ensure items don't exceed sidebar boundaries */
    max-width: calc(100% - 1rem); /* Leave 16px total margin */
  }

  /* Prevent profile section overflow in collapsed mode */
  .sidebar-dark .sidebar-profile.sidebar-item-collapsed {
    position: relative;
    left: 0;
    transform: none;
  }

    /* Enhanced sidebar typography */
    .sidebar-label {
      @apply text-base-app font-medium; /* Uses the increased font size */
    }

    .sidebar-sublabel {
      @apply text-sm-app; /* Uses the increased font size */
    }

    .sidebar-brand {
      @apply text-lg-app font-bold; /* Uses the increased font size */
    }

    .sidebar-profile-name {
      @apply text-sm-app font-medium; /* Uses the increased font size */
    }

    .sidebar-profile-role {
      @apply text-xs-app; /* Uses the increased font size */
    }
  }  /* Removed glassmorphism effects since sidebar now matches header solid background */

  /* Fixed Issue #1: Removed transform rules that cause active item shifting */
  /* .sidebar-dark .sidebar-nav-item:not(.sidebar-item-collapsed):hover {
    transform: translateX(2px);
  }

  .sidebar-dark .sidebar-nav-item:not(.sidebar-item-collapsed).active {
    transform: translateX(4px);
  } *//* Subtle glow effects */
  .sidebar-dark .sidebar-brand {
    filter: drop-shadow(0 0 12px rgba(96, 165, 250, 0.4));
  }

  /* Enhanced profile section */
  .sidebar-dark .sidebar-profile {
    @apply mx-2 rounded-xl;
    backdrop-filter: blur(10px);
  }
